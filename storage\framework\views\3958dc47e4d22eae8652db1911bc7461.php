<style>
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }
</style>
<form class="clearfix" method="post"
    action="<?php echo e($mem_id ? config('patient.url') . 'updateFamilyPost/' . $id . '/' . $mem_id : config('patient.url') . 'addFamilyPost/' . $id); ?>"
    data-mode="<?php echo e($mem_id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <input type="hidden" name="stat_membership" value="<?php echo e(isset($stat) ? 1 : 0); ?>">
    <div class="row">
        <div class="form-group col-md-4">
            <label class="form-label fw-bold" for="fname">Name <b class="text-primary">*</b></label>
            <input type="text" class="form-control form-control-sm" name="name" value="<?php echo e($mem_id ? $data['name'] : ''); ?>" placeholder="">
        </div>
        <div class="form-group col-md-4">
            <label class="form-label fw-bold" for="fname">Parent Phone</label>
            <input type="text" class="form-control form-control-sm" value="<?php echo e($phone); ?>" readonly>
        </div>
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Gender:</label>
            <select id="sex" name="sex" class="select2-multpl-custom1 form-select"
              data-style="py-0">
                <?php $__currentLoopData = $list['gender_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($row); ?>" <?php echo e($mem_id ? ($data['sex'] == $row ? 'selected' : '') : ''); ?>><?php echo e($row); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="form-group col-md-4">
            <label class="form-label fw-bold" for="cname">DOB <b class="text-primary">*</b></label>
            <input type="date" class="form-control form-control-sm" name="birthdate" id="birthdate"
                value="<?php echo e($mem_id ? $data['birthdate'] : date('Y-m-d')); ?>" placeholder="" max="<?php echo e(date('Y-m-d')); ?>"
                onchange="calculateAge()">
        </div>
        <div class="form-group col-md-4">
            <label class="form-label fw-bold" for="cname">Age</label>
            <div class="row gx-2">
                <div class="form-group col-md-4">
                    <div class="d-flex gap-1 align-items-center">
                        <input type="number" class="form-control form-control-sm" id="age_years" min="0"
                            value="0" placeholder="" onkeyup="calculateDOB()">
                        <label class="form-label fw-normal h7 text-gray mb-0" for="cname">Years</label>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    <div class="d-flex gap-1 align-items-center">
                        <input type="number" class="form-control form-control-sm" id="age_months" min="0"
                            value="0" placeholder="" onkeyup="calculateDOB()">
                        <label class="form-label fw-normal h6 text-gray mb-0" for="cname">Months</label>
                    </div>
                </div>
                <div class="form-group col-md-4">
                    <div class="d-flex gap-1 align-items-center">
                        <input type="number" class="form-control form-control-sm" id="age_days" min="0"
                            value="0" placeholder="" onkeyup="calculateDOB()">
                        <label class="form-label fw-normal h6 text-gray mb-0" for="cname">Days</label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Language <b class="text-primary">*</b></label>
            <select id="language_id" name="language_id" class="select2-multpl-custom1 form-select"
              data-style="py-0">
                <option value="">Select language</option>
                <?php $__currentLoopData = $list['language_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($row->id); ?>" <?php echo e($mem_id ? ($data['language_id'] == $row->id ? 'selected' : '') : ''); ?>><?php echo e($row->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="form-group col-md-4">
            <label for="exampleInputEmail1" class="form-label fw-bold">Relationship With Patient <b class="text-primary">*</b></label>
            <select id="relationship_id" name="relationship_id" class="select2-multpl-custom1 form-select"
              data-style="py-0">
                <option value="">Select relationship</option>
                <?php $__currentLoopData = $list['relationship_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($row->id); ?>" <?php echo e($mem_id ? ($data['relationship_id'] == $row->id ? 'selected' : '') : ''); ?>><?php echo e($row->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="form-group col-md-4">
            <label class="fw-bold d-block mb-2 text-dark">Employee</label>
            <div class="form-check form-check-inline ps-0">
                <input type="checkbox" id="is_employee" name="is_employee" value="2" <?php echo e($mem_id ? ($data['is_employee'] == 2 ? 'checked' : '') : ''); ?>> <label for="is_employee" class="fw-normal">Is Employee</label>
            </div>
        </div>
        <div class="form-group col-md-12 text-end">
            <button type="submit" name="submit"
                class="btn btn-primary text-white"><?php echo e($mem_id ? 'Update' : 'Add'); ?> Member</button>
        </div>
        <div class="form-group col-md-12">
            <div id="errorMessage" class="" style="color: red;"></div>
        </div>
    </div>
</form>
<script>
    function calculateAge() {
        let birthdate = $('#birthdate').val();
        const startDate = new Date(birthdate);
        const currentDate = new Date();

        let years = currentDate.getFullYear() - startDate.getFullYear();
        let months = currentDate.getMonth() - startDate.getMonth();
        let days = currentDate.getDate() - startDate.getDate();

        // Adjust months and years if current month/day is before birth month/day
        if (days < 0) {
            months--;
            // Get days in previous month
            const previousMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);
            days += previousMonth.getDate();
        }

        if (months < 0) {
            years--;
            months += 12;
        }

        $('#age_years').val(years);
        $('#age_months').val(months);
        $('#age_days').val(days);
    }

    function calculateDOB() {
        let age_years = parseInt($('#age_years').val()) || 0;
        let age_months = parseInt($('#age_months').val()) || 0;
        let age_days = parseInt($('#age_days').val()) || 0;

        let today = new Date();
        let birthdate = new Date(
            today.getFullYear() - age_years,
            today.getMonth() - age_months,
            today.getDate() - age_days
        );

        // Format to YYYY-MM-DD
        let yyyy = birthdate.getFullYear();
        let mm = String(birthdate.getMonth() + 1).padStart(2, '0'); // Month is 0-based
        let dd = String(birthdate.getDate()).padStart(2, '0');

        let formattedDate = `${yyyy}-${mm}-${dd}`;
        console.log(formattedDate);
        $('#birthdate').val(formattedDate);
    }
    <?php if($id): ?>
        calculateAge();
    <?php endif; ?>
</script><?php /**PATH C:\wamp64\www\mymd-care\Modules/Patient\resources/views/patient/api/addFamily.blade.php ENDPATH**/ ?>