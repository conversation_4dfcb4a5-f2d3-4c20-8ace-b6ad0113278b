<option value="">Select</option>
<?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <option value="<?php echo e(isset($row['clinic_id']) ? $row['clinic_id'] : $row['name']); ?><?php echo e(isset($row['schedule_id']) ? (','.$row['schedule_id']) : ''); ?>"><?php echo e($row['name']); ?></option>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/api/slotDropdown.blade.php ENDPATH**/ ?>