<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td>
            <?php echo e($row['users']['username'] ?? ''); ?>

        </td>
        <td><?php echo e($row['users']['schedule_doctors']['doctor_type']['title'] ?? ''); ?></td>
        <td><?php echo e($row['clinics']['clinic_name']); ?></td>
        <td><?php echo e($row['date']); ?></td>
        <td><?php echo e($row['s_time']); ?> to <?php echo e($row['e_time']); ?></td>
        <td id="time_in<?php echo e($row['id']); ?>"><?php echo e($row['time_in']); ?></td>
        <td id="time_out<?php echo e($row['id']); ?>"><?php echo e($row['time_out']); ?></td>
        <td>
            <div class="d-flex flex-nowrap gap-2">
                <?php if(array_intersect(['change_status_schedule'], $permissionPage) && $row['is_time'] != 3 && strtotime(date('Y-m-d')) == strtotime($row['date']) && $row['status'] != 0): ?>
                    <?php
                        $status = $row['is_time'] == 2 ? 1 : 3;
                    ?>
                    <a href="#" title="<?php echo e($row['is_time'] == 2 ? 'Time In' : 'Time Out'); ?>"
                        class="btn btn-primary btn-sm" role="button" data-stat="<?php echo e($status); ?>"
                        onclick="updateTimeInOut('<?php echo e(config('schedule.url') . 'updateTimeInOut/' . $row['id']); ?>','POST',this)">
                        <?php echo e($row['is_time'] == 2 ? 'Time In' : 'Time Out'); ?>

                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['delete_schedule'], $permissionPage)): ?>
                    <?php
                        $status = $row['status'] == 0 ? 1 : 0;
                    ?>
                    <a href="#" title="<?php echo e($row['status'] == 0 ? 'Deactive' : 'Active'); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" data-stat="<?php echo e($status); ?>"
                        onclick="updateStatus('<?php echo e(config('schedule.url') . 'delete/' . $row['id']); ?>','POST',this)">
                        <svg class=" text-primary" xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24"
                            fill="currentColor">
                            <g>
                                <circle cx="12" cy="12" r="8"
                                    fill="<?php echo e($row['status'] == 0 ? '#F10F0F' : '#13B907'); ?>"></circle>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
            </div>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Schedule\resources/views/schedule/api/list.blade.php ENDPATH**/ ?>