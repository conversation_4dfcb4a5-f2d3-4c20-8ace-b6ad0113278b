<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td><?php echo e($row['registration_date']); ?></td>
        <td><?php echo e($row['name']); ?></td>
        <td><?php echo e($row['phone']); ?></td>
        <td><?php echo e($row['start_date']); ?> To <?php echo e($row['end_date']); ?></td>
        <td><?php echo e($row['registration_no']); ?></td>
        <td><?php echo e($row['memberships']['name'] ?? ''); ?></td>
        <td><?php echo e($row['clinics']['clinic_name'] ?? ''); ?></td>
        <td>
            <div class="d-flex flex-nowrap gap-2">
                <?php if(array_intersect(['create_agent_billing'], $permissionPage)): ?>
                    <?php echo e($row['payment_bill'][0]['status'] == 1 ? 'Paid' : 'Due'); ?> Payment
                <?php endif; ?>
                
                <?php if(array_intersect(['invoice_billing'], $permissionPage)): ?>
                    <a href="<?php echo e(route('billing.viewInvoice', ['t' => base64_encode($row['id'])])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="MemberShip Invoice" role="button" target="_blank">
                        <svg version="1.1" id="fi_85966" width="18" height="18" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 491.695 491.695"
                            style="enable-background:new 0 0 491.695 491.695;" xml:space="preserve">
                            <g>
                                <path d="M436.714,0H149.471c-16.438,0-29.812,13.374-29.812,29.812v66.714c-54.49,15.594-94.489,65.857-94.489,125.288
                            c0,59.431,39.998,109.694,94.489,125.288v114.783c0,16.438,13.374,29.812,29.812,29.812h234.733c2.785,0,5.455-1.106,7.425-3.075
                            l71.821-71.822c1.969-1.969,3.075-4.64,3.075-7.425V29.812C466.525,13.374,453.152,0,436.714,0z M149.471,21h287.243
                            c4.858,0,8.811,3.953,8.811,8.812v31.689H140.659V29.812C140.659,24.953,144.612,21,149.471,21z M46.17,221.813
                            c0-60.263,49.027-109.29,109.29-109.29c60.263,0,109.29,49.027,109.29,109.29s-49.027,109.291-109.29,109.291
                            C95.197,331.104,46.17,282.076,46.17,221.813z M140.659,461.884V351.258c4.86,0.552,9.797,0.846,14.802,0.846
                            c39.135,0,74.292-17.347,98.195-44.752h64.336c5.799,0,10.5-4.701,10.5-10.5s-4.701-10.5-10.5-10.5h-49.381
                            c9.133-15.95,14.984-34.005,16.644-53.242h32.736c5.799,0,10.5-4.701,10.5-10.5c0-5.799-4.701-10.5-10.5-10.5h-32.603
                            c-1.42-19.194-7.02-37.242-15.886-53.241h48.488c5.799,0,10.5-4.701,10.5-10.5c0-5.799-4.701-10.5-10.5-10.5h-62.974
                            c-23.918-28.323-59.67-46.347-99.558-46.347c-5.005,0-9.942,0.294-14.802,0.846v-9.867h304.866v316.372h-42.009
                            c-16.439,0-29.811,13.374-29.811,29.811v42.011H149.471C144.612,470.695,140.659,466.743,140.659,461.884z M394.705,455.845v-27.16
                            c0-4.859,3.953-8.811,8.811-8.811h27.16L394.705,455.845z"></path>
                                <path d="M359.246,158.869h34.87c5.799,0,10.5-4.701,10.5-10.5c0-5.799-4.701-10.5-10.5-10.5h-34.87c-5.799,0-10.5,4.701-10.5,10.5
                            C348.746,154.168,353.447,158.869,359.246,158.869z"></path>
                                <path d="M359.246,233.11h34.87c5.799,0,10.5-4.701,10.5-10.5c0-5.799-4.701-10.5-10.5-10.5h-34.87c-5.799,0-10.5,4.701-10.5,10.5
                            C348.746,228.409,353.447,233.11,359.246,233.11z"></path>
                                <path d="M359.246,307.352h34.87c5.799,0,10.5-4.701,10.5-10.5s-4.701-10.5-10.5-10.5h-34.87c-5.799,0-10.5,4.701-10.5,10.5
                            S353.447,307.352,359.246,307.352z"></path>
                                <path d="M394.116,381.593c5.799,0,10.5-4.701,10.5-10.5s-4.701-10.5-10.5-10.5h-98.225c-5.799,0-10.5,4.701-10.5,10.5
                            s4.701,10.5,10.5,10.5H394.116z"></path>
                                <path d="M236.982,168.845l-12.81-12.81c-3.45-3.449-8.036-5.349-12.915-5.349s-9.465,1.9-12.915,5.349l-67.19,67.19l-18.573-18.573
                            c-3.449-3.448-8.036-5.348-12.914-5.348c-4.878,0-9.465,1.9-12.914,5.349l-12.813,12.812c-7.12,7.121-7.12,18.708,0.001,25.829
                            l44.297,44.296c3.45,3.451,8.037,5.351,12.916,5.351c0,0,0.001,0,0.001,0c4.878,0,9.465-1.9,12.913-5.349l92.917-92.917
                            C244.103,187.554,244.103,175.966,236.982,168.845z M131.151,270.807l-40.429-40.428l8.942-8.942l24.062,24.062
                            c4.101,4.101,10.749,4.101,14.85,0l72.681-72.681l8.942,8.942L131.151,270.807z"></path>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                            <g>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['card_billing'], $permissionPage) && $row['smart_card'] == 1): ?>
                    <a href="<?php echo e(route('billing.cardBill', [base64_encode($row['id'])])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="AB SMART CARD Bill" role="button">
                        <svg height="20" viewBox="0 -11 493.78 493" width="20" xmlns="http://www.w3.org/2000/svg"
                            id="fi_1611154">
                            <path
                                d="m378.351562 70.472656c.214844.015625.429688.03125.648438.03125.371094 0 .742188-.03125 1.105469-.082031 9.722656.199219 17.503906 8.128906 17.515625 17.851563 0 4.417968 3.582031 8 8 8 4.417968 0 8-3.582032 8-8-.019532-15.902344-11.089844-29.660157-26.621094-33.082032v-7.6875c0-4.417968-3.582031-8-8-8s-8 3.582032-8 8v8.050782c-16.421875 4.390624-27.046875 20.277343-24.832031 37.132812 2.214843 16.855469 16.582031 29.457031 33.582031 29.457031 9.871094 0 17.871094 8.003907 17.871094 17.875 0 9.867188-8 17.871094-17.871094 17.871094s-17.871094-8.003906-17.871094-17.871094c0-4.417969-3.582031-8-8-8-4.417968 0-8 3.582031-8 8 .019532 15.328125 10.316406 28.738281 25.121094 32.71875v8.765625c0 4.417969 3.582031 8 8 8s8-3.582031 8-8v-8.398437c16.894531-3.699219 28.289062-19.535157 26.425781-36.730469-1.859375-17.195312-16.378906-30.226562-33.675781-30.222656-9.597656.003906-17.484375-7.574219-17.863281-17.164063-.375-9.589843 6.894531-17.765625 16.464843-18.511719zm0 0">
                            </path>
                            <path
                                d="m380.207031.390625c-49.214843 0-91.214843 32.113281-106.949219 75.113281h-198.558593c-4.398438 0-7.96875 3.964844-8 8.359375l-1.890625 280.640625h-56.597656c-4.417969 0-8.210938 3.199219-8.210938 7.625v35.613282c.101562 33.527343 26.507812 61.070312 60 62.585937v.175781h247v-.234375c2 .074219 2.824219.234375 4.089844.234375h.171875c34.664062-.054687 62.738281-28.171875 62.738281-62.835937v-180.0625c2 .109375 4.117188.167969 6.1875.167969 62.628906 0 113.59375-51.0625 113.59375-113.695313 0-62.628906-50.941406-113.6875-113.574219-113.6875zm-317.164062 454.113281h-.050781c-25.878907-.035156-46.875-20.960937-46.992188-46.84375v-27.15625h232v27.042969c.011719 16.695313 6.679688 32.699219 18.523438 44.46875.839843.839844 1.882812 1.488281 2.761718 2.488281zm294.957031-46.84375c.003906 25.835938-20.914062 46.792969-46.746094 46.84375h-.152344c-25.9375-.046875-46.972656-21.015625-47.101562-46.949218v-35.425782c.066406-2.046875-.714844-4.027344-2.164062-5.472656-1.449219-1.445312-3.429688-2.222656-5.472657-2.152344h-175.554687l1.835937-273h186.171875c-1.417968 7.324219-2.152344 14.761719-2.191406 22.21875-.015625 15.769532 3.273438 31.363282 9.65625 45.78125h-75.5625c-4.421875 0-8 3.582032-8 8 0 4.417969 3.578125 8 8 8h84.242188c16.503906 25.953125 42.886718 44.046875 73.039062 50.101563zm22.207031-195.882812c-53.890625 0-97.582031-43.6875-97.578125-97.582032 0-53.894531 43.6875-97.582031 97.582032-97.582031 53.890624 0 97.578124 43.691407 97.578124 97.582031-.058593 53.867188-43.710937 97.523438-97.582031 97.582032zm0 0">
                            </path>
                            <path
                                d="m149.367188 212.746094c-14.121094 0-25.605469 11.121094-25.605469 24.792968 0 13.671876 11.484375 24.792969 25.605469 24.792969 14.121093 0 25.609374-11.121093 25.609374-24.792969 0-13.671874-11.488281-24.792968-25.609374-24.792968zm0 33.585937c-5.300782 0-9.605469-3.945312-9.605469-8.792969 0-4.851562 4.308593-8.792968 9.605469-8.792968 5.296874 0 9.609374 3.945312 9.609374 8.792968 0 4.847657-4.3125 8.792969-9.609374 8.792969zm0 0">
                            </path>
                            <path
                                d="m192.71875 237.503906c0 4.417969 3.578125 8 8 8h106.65625c4.417969 0 8-3.582031 8-8 0-4.417968-3.582031-8-8-8h-106.65625c-4.421875 0-8 3.582032-8 8zm0 0">
                            </path>
                            <path
                                d="m149.367188 143.203125c-14.121094 0-25.605469 11.125-25.605469 24.796875s11.484375 24.792969 25.605469 24.792969c14.121093 0 25.609374-11.121094 25.609374-24.792969s-11.488281-24.796875-25.609374-24.796875zm0 33.589844c-5.300782 0-9.605469-3.945313-9.605469-8.792969s4.308593-8.796875 9.605469-8.796875c5.296874 0 9.609374 3.945313 9.609374 8.796875 0 4.847656-4.3125 8.796875-9.609374 8.796875zm0 0">
                            </path>
                            <path
                                d="m149.367188 282.28125c-14.121094 0-25.605469 11.121094-25.605469 24.792969s11.484375 24.792969 25.605469 24.792969c14.121093 0 25.609374-11.121094 25.609374-24.792969s-11.488281-24.792969-25.609374-24.792969zm0 33.585938c-5.300782 0-9.605469-3.941407-9.605469-8.792969 0-4.847657 4.308593-8.792969 9.605469-8.792969 5.296874 0 9.609374 3.945312 9.609374 8.792969 0 4.847656-4.3125 8.792969-9.609374 8.792969zm0 0">
                            </path>
                            <path
                                d="m307.375 299.503906h-106.65625c-4.421875 0-8 3.582032-8 8 0 4.417969 3.578125 8 8 8h106.65625c4.417969 0 8-3.582031 8-8 0-4.417968-3.582031-8-8-8zm0 0">
                            </path>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['change_status_billing'], $permissionPage)): ?>
                    <?php
                        $status = $row['status'] == 1 ? 3 : 1;
                    ?>
                    <a href="#" title="Membership <?php echo e($row['status'] == 3 ? 'Deactive' : 'Active'); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" data-stat="<?php echo e($status); ?>"
                        onclick="updateStatusMembership('<?php echo e(config('billing.url') . 'updateStatus/' . $row['id']); ?>','POST',this)">
                        <svg height="20" viewBox="0 0 32 32" width="20" xmlns="http://www.w3.org/2000/svg"
                            id="fi_6049203">
                            <g id="credit_card_failed" fill-rule="evenodd" data-name="credit card failed">
                                <path d="m24 14a7 7 0 1 0 7 7 7 7 0 0 0 -7-7zm0 12a5 5 0 1 1 5-5 5 5 0 0 1 -5 5z">
                                </path>
                                <path
                                    d="m26.671 18.329a1 1 0 0 0 -1.414 0l-1.257 1.257-1.257-1.257a1 1 0 1 0 -1.414 1.414l1.257 1.257-1.257 1.257a1 1 0 1 0 1.414 1.414l1.257-1.257 1.257 1.257a1 1 0 0 0 1.414-1.414l-1.257-1.257 1.257-1.257a1 1 0 0 0 0-1.414z">
                                </path>
                                <path
                                    d="m27 4h-22a4 4 0 0 0 -4 4v12a4 4 0 0 0 4 4h10a1 1 0 0 0 0-2h-10a2 2 0 0 1 -2-2v-8h19a1 1 0 0 0 0-2h-19v-2a2 2 0 0 1 2-2h22a2 2 0 0 1 2 2v2h-3a1 1 0 0 0 0 2h3v2a1 1 0 0 0 2 0v-6a4 4 0 0 0 -4-4z">
                                </path>
                                <path d="m6 18a1 1 0 0 0 0 2h4a1 1 0 0 0 0-2z"></path>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
            </div>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Billing\resources/views/billing/api/list.blade.php ENDPATH**/ ?>