<?php $__env->startSection('title'); ?>
    <?php echo e(config('prescription.title', 'Prescription')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-body placeholder-glow" id="data-add-edit">
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title placeholder"></h4>
                    </div>
                </div>
                <form class="clearfix" method="post"
                    action="<?php echo e(config('prescription.url') . 'uploadPrescription/' . $id); ?>"
                    data-mode="add" enctype="multipart/form-data" id="submitForm">
                    <div class="row">
                        <div class="col-md-12 col-lg-4 pe-3 pt-3">
                            <div class="form-group col-md-12">
                                <label class="form-label fw-bold h8 text-gray" for="next_followup_date">Upload File * (jpg, png, pdf)</label>
                                <div class="col-12 col-md-12">
                                    <input type="file" class="form-control form-control-sm bg-light text-dark h8"
                                        name="files" value="">
                                </div>
                            </div>
                        </div>
                        <div class="form-group col-md-12">
                            <button type="submit" name="submit"
                                class="btn btn-primary text-white">Upload</button>
                        </div>
                        <div class="form-group col-md-12">
                            <div id="errorMessage" class="" style="color: red;"></div>
                        </div>
                    </div>
                </form>

            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            refreshToken();
            let redirectUrl = "<?php echo e(route('appointment.index',[($stat == 'all' ? $stat : 'today')])); ?>";
            setRedirectUrl(redirectUrl);

            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Prescription\resources/views/prescription/uploadPrescription.blade.php ENDPATH**/ ?>