<?php $__env->startSection('title'); ?>
    <?php echo e(config('patient.title', 'Patient')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title"><?php echo e($id ? 'Update' : 'Add'); ?> Patient</h4>
                </div>
                <div class="header-action">
                    <a href="<?php echo e(request('stat') == 'DG' ? route('diagnostic.addForm') : route('patient.index')); ?>"
                        class="btn btn-primary btn-sm d-flex gap-1 align-items-center justify-content-center">
                        Back
                    </a>
                </div>
            </div>
            <div class="card-body placeholder-glow" id="data-add-edit">
                <?php echo $__env->make('admin.custom.loadingForm', ['fields' => [4,4,4,4,4,4,4,4]], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        // function calculateAge() {
        //     let birthdate = $('#birthdate').val();
        //     const startDate = new Date(birthdate);
        //     const currentDate = new Date();
        //     let yearsDifference = currentDate.getFullYear() - startDate.getFullYear();
        //     // Adjust the year difference if the current date hasn't reached the start date yet this year
        //     const isBeforeAnniversary = (currentDate.getMonth() < startDate.getMonth()) || 
        //                                 (currentDate.getMonth() === startDate.getMonth() && currentDate.getDate() < startDate.getDate());

        //     if (isBeforeAnniversary) {
        //         yearsDifference--;
        //     }
        //     $('#age').val(yearsDifference);
        // }
        // function calculateDOB() {
        //     let age = $('#age').val();
        //     console.log(age);
        //     let today = new Date();
        //     today.setFullYear(today.getFullYear() - age); // Subtract one year
        //     let year = today.getFullYear();
        //     let date = `${year}-01-01`;
        //     console.log(date);
        //     $('#birthdate').val(date);
        // }
        $(document).ready(function() {
            let redirectUrl = "<?php echo e(request('stat') == 'DG' ? route('diagnostic.addForm') : route('patient.index')); ?>";
            if ("<?php echo e(request('stat')); ?>" == 'DG') {
                let redirectUrlData = localStorage.getItem('redirect_url_data');
                redirectUrl += redirectUrlData;
            }
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            setId(ht_id); // set show table id
            let url = "<?php echo e($id ? config('patient.url') . 'edit/'.$id : config('patient.url') . 'create'); ?>";
            setListUrl(url); // api url for show table
            let method = 'GET';
            setMethod(method);
            let filter = {};
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter

            $(document).on("submit","#submitForm",function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Patient\resources/views/patient/add.blade.php ENDPATH**/ ?>