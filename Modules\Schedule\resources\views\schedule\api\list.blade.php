@foreach ($data['rows'] as $row)
    <tr>
        <td>{{ $row['id'] }}</td>
        <td>
            {{ $row['users']['username'] ?? '' }}
        </td>
        <td>{{ $row['users']['schedule_doctors']['doctor_type']['title'] ?? '' }}</td>
        <td>{{ $row['clinics']['clinic_name'] }}</td>
        <td>{{ $row['date'] }}</td>
        <td>{{ $row['s_time'] }} to {{ $row['e_time'] }}</td>
        <td id="time_in{{ $row['id'] }}">{{ $row['time_in'] }}</td>
        <td id="time_out{{ $row['id'] }}">{{ $row['time_out'] }}</td>
        <td>
            <div class="d-flex flex-nowrap gap-2">
                @if (array_intersect(['change_status_schedule'], $permissionPage) && $row['is_time'] != 3 && strtotime(date('Y-m-d')) == strtotime($row['date']) && $row['status'] != 0)
                    @php
                        $status = $row['is_time'] == 2 ? 1 : 3;
                    @endphp
                    <a href="#" title="{{ $row['is_time'] == 2 ? 'Time In' : 'Time Out' }}"
                        class="btn btn-primary btn-sm" role="button" data-stat="{{ $status }}"
                        onclick="updateTimeInOut('{{ config('schedule.url') . 'updateTimeInOut/' . $row['id'] }}','POST',this)">
                        {{ $row['is_time'] == 2 ? 'Time In' : 'Time Out' }}
                    </a>
                @endif
                @if (array_intersect(['delete_schedule'], $permissionPage))
                    @php
                        $status = $row['status'] == 0 ? 1 : 0;
                    @endphp
                    <a href="#" title="{{ $row['status'] == 0 ? 'Deactive' : 'Active' }}"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" data-stat="{{ $status }}"
                        onclick="updateStatus('{{ config('schedule.url') . 'delete/' . $row['id'] }}','POST',this)">
                        <svg class=" text-primary" xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24"
                            fill="currentColor">
                            <g>
                                <circle cx="12" cy="12" r="8"
                                    fill="{{ $row['status'] == 0 ? '#F10F0F' : '#13B907' }}"></circle>
                            </g>
                        </svg>
                    </a>
                @endif
            </div>
        </td>
    </tr>
@endforeach
