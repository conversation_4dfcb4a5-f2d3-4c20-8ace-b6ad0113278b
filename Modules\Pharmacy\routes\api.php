<?php

use Illuminate\Support\Facades\Route;
use Modules\Pharmacy\Http\Controllers\PharmacyController;
use Modules\Pharmacy\Http\Controllers\CategoryController;
use Modules\Pharmacy\Http\Controllers\MedicineController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/


Route::middleware(['auth:api'])->prefix('pharmacyCategory')->group(function () {
    Route::middleware(['permission:view_pharmacy_category'])->post('/list', [CategoryController::class, 'list']);
    Route::middleware(['permission:create_pharmacy_category'])->get('/create', [CategoryController::class, 'create']);
    Route::middleware(['permission:create_pharmacy_category'])->post('/add', [CategoryController::class, 'add']);
    Route::middleware(['permission:edit_pharmacy_category'])->get('/edit/{id}', [CategoryController::class, 'edit']);
    Route::middleware(['permission:edit_pharmacy_category'])->post('/update/{id}', [CategoryController::class, 'update']);
    Route::middleware(['permission:change_status_pharmacy_category'])->post('/updateStatus/{id}', [CategoryController::class, 'updateStatus']);
    Route::middleware(['permission:info_pharmacy_category'])->get('/detail/{id}', [CategoryController::class, 'detail']);
    Route::middleware(['permission:delete_pharmacy_category'])->get('/delete/{id}', [CategoryController::class, 'delete']);
});

Route::middleware(['auth:api'])->prefix('pharmacyMedicine')->group(function () {
    Route::middleware(['permission:view_pharmacy_medicine'])->post('/list', [MedicineController::class, 'list']);
    Route::middleware(['permission:create_pharmacy_medicine'])->get('/create', [MedicineController::class, 'create']);
    Route::middleware(['permission:create_pharmacy_medicine'])->post('/add', [MedicineController::class, 'add']);
    Route::middleware(['permission:edit_pharmacy_medicine'])->get('/edit/{id}', [MedicineController::class, 'edit']);
    Route::middleware(['permission:edit_pharmacy_medicine'])->post('/update/{id}', [MedicineController::class, 'update']);
    Route::middleware(['permission:change_status_pharmacy_medicine'])->post('/updateStatus/{id}', [MedicineController::class, 'updateStatus']);
    Route::middleware(['permission:info_pharmacy_medicine'])->get('/detail/{id}', [MedicineController::class, 'detail']);
    Route::middleware(['permission:delete_pharmacy_medicine'])->get('/delete/{id}', [MedicineController::class, 'delete']);
});
Route::middleware(['auth:api'])->prefix('pharmacyOrder')->group(function () {
    Route::middleware(['permission:view_pharmacy_order'])->post('/list', [PharmacyController::class, 'list']);
    Route::middleware(['permission:add_pharmacy_order|add_pharmacy_order_agent'])->post('/create', [PharmacyController::class, 'create']);
    Route::middleware(['permission:add_pharmacy_order|add_pharmacy_order_agent'])->post('/listFamily', [PharmacyController::class, 'listFamily']);
    Route::middleware(['permission:add_pharmacy_order'])->post('/listSlot', [PharmacyController::class, 'listSlot']);
    Route::middleware(['permission:add_pharmacy_order|add_pharmacy_order_agent'])->post('/add', [PharmacyController::class, 'add']);
    Route::middleware(['permission:edit_pharmacy_order'])->post('/update/{id}', [PharmacyController::class, 'update']);
    Route::middleware(['permission:edit_pharmacy_order'])->post('/edit/{id}', [PharmacyController::class, 'edit']);
    Route::middleware(['permission:change_status_pharmacy_order'])->post('/updateStatus/{id}', [PharmacyController::class, 'updateStatus']);
    Route::middleware(['permission:change_status_pharmacy_order'])->post('/acceptOrder', [PharmacyController::class, 'acceptOrder']);
    Route::middleware(['permission:change_status_pharmacy_order'])->post('/rejectOrder', [PharmacyController::class, 'rejectOrder']);
    Route::middleware(['permission:add_pharmacy_order|add_pharmacy_order_agent'])->post('/sendOtpBeforeSubmit', [PharmacyController::class, 'sendOtpBeforeSubmit']);
    Route::middleware(['permission:add_pharmacy_order|add_pharmacy_order_agent'])->post('/verifyOtpBeforeSubmit', [PharmacyController::class, 'verifyOtpBeforeSubmit']);
    Route::middleware(['permission:add_pharmacy_order|add_pharmacy_order_agent'])->post('/prescriptionUpload', [PharmacyController::class, 'prescriptionUpload']);
});
