<script>
    function openPaymentMode(parent) {
        let total_due = $('#total_due').val();
        if (total_due == 0) {
            $(parent).prop('checked',false);
            alert('Payment mode cannot be made as no due price is currently available.');
            return false;
        }
        if ($(parent).prop('checked')) {
            let lable = $(parent).attr('data-id');

            let html = paymentModes(parent.value, lable);
            // console.log(html);
            $('.show_payment').append(html);
        } else {
            $('#row-' + parent.value).remove();

        }
    }

    function paymentModes(key, lable) {
        let html = '<div class="col-md-12" id="row-' + key + '">';
        html += '<div class="border-top py-3">';
        html += '<div class="row align-items-center">';
        html += '<div class="col-md-2">';
        html += '<input type="hidden" name="payment_modes[]" value="' + key + '">';
        html += '<p class=" fw-bold text-dark m-0 mb-3 mb-md-0">' + lable + '</p>';
        html += '</div>';

        html += '<div class="col-md-10">';
        html += '<div class="row">';

        html += '<div class="col-md-4 mb-2 mb-md-0">';

        html += '<div class="row align-items-center ">';
        html += '<label class="col-5 col-md-5">Paid Amount</label>';
        html += '<div class="col-7 col-md-7">';
        html += '<input class="form-control form-control-sm amount_class " type="number" min="0" name="amounts[]">';
        html += '</div>';
        html += '</div>';
        html += '</div>';

        html += '<div class="col-md-4" style="' + ((key == 'cash' || key == 'refund_reward_points') ? 'display:none' :
            '') + '">';
        html += '<div class="row align-items-center ">';
        html += '<label class="col-5 col-md-5">Transaction ID</label>';
        html += '<div class="col-7 col-md-7">';
        html += '<input class="form-control form-control-sm " type="text" name="payment_list[]" ' + ((key == 'cash' ||
            key == 'refund_reward_points') ? '' : 'required') + '>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        if (key == 'upi') {
            html += '<div class="col-md-4">';
            html += '<div class="row align-items-center">';
            html += '<label for="exampleInputEmail1" class="col-5 col-md-3">Mode</label>';
            html += '<div class="col-7 col-md-9">';
            html +=
                '<select class="selectpicker form-control form-select form-select-sm pe-4" name="upi_mode" id="upi_mode">';
            html += '<option value="phone_pay"> Phone Pay </option>';
            html += '<option value="paytm"> Paytm </option>';
            html += '<option value="google_pay"> Google Pay </option>';
            html += '</select>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
        }
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        return html;
    }
    $(document).on("keyup", ".amount_class", function() {
        paidCalculation();
    });

    function paidCalculation() {
        if (Number($('#payable_amount').val()) == 0) {
            emptyPaymentMode();
        }
        let total = 0;
        $(".amount_class").each(function() {
            if ($(this).val()) {
                total += parseFloat($(this).val());
            }
        });
        let due = parseFloat($("#payable_amount").val());
        if (total > due) {
            alert('Paid amount can not be greater than due amount');
            $(".amount_class").val('');
            paidCalculation();
            return false;
        }
        console.log(total,due);
        
        $("#total_amount").val(total);
        $("#total_due").val(due - total);
        let percentage = $("#min_payment_percentage").val();
        percentage = percentage ? percentage : 100;
        if ((total >= ((due * percentage) / 100).toFixed(2)) || due == 0) {
            $('#submitBtn').prop('disabled', false);
        } else {
            $('#submitBtn').prop('disabled', true);
        }
    }

    function emptyPaymentMode() {
        $('#total_amount').val(0);
        $('#total_due').val(0);
        $('.show_payment').html('');
        $('.hobbies_class').prop('checked', false);
        return false;
    }
</script>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Billing\resources/views/billing/js/payment.blade.php ENDPATH**/ ?>