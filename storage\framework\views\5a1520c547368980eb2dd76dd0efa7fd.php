<form class="clearfix" method="post"
    action="<?php echo e($id ? config('pharmacy.order_url') . 'update/' . $id : config('pharmacy.order_url') . 'add'); ?>"
    data-mode="<?php echo e($id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <?php if(!$id): ?>
        <input type="hidden" name="patient_id" value="<?php echo e($data['patient_detail']->id); ?>">
        <input type="hidden" name="patient_phone" id="verify_otp_phone" value="<?php echo e($phone); ?>">
        <input type="hidden" class="" id="latitude" name="latitude" value="<?php echo e($data['latitude']); ?>">
        <input type="hidden" class="" id="longitude" name="longitude" value="<?php echo e($data['longitude']); ?>">
        <input type="hidden" name="agent_source_id" value="<?php echo e($data['agent_source_id']); ?>">
    <?php endif; ?>
    <?php if(!$id): ?>
        <div class="row" data-select2-id="select2-data-16-ukk2">
            <div class="col-sm-12" data-select2-id="select2-data-15-62ts">
                <!------------Top card starts here----------->
                <div class="card  mb-3" data-select2-id="select2-data-14-bl8l">
                    <div class="card-header border-bottom p-4" style="background-color: #fff8f8;">
                        <div class=" row">
                            <!--Form header starts here-->
                            <div class="col-md-12 mb-0 mb-md-0">
                                <!-- <h5 class="h6 mb-1 mb-md-3 fw-bold">Patient Details</h6> -->
                                <div class="d-flex gap-4 flex-wrap">
                                    <div class="d-flex align-items-center me-5">
                                        <h6 class="h5 fw-bold text-primary">
                                            <span><?php echo e($data['patient_detail']->name); ?></span>
                                        </h6> &nbsp;<span class="h7" style="margin-top: 4px;">
                                            (<span><?php echo e($data['patient_detail']->sex); ?></span>/<span><?php echo e(Helper::ageCalculator($data['patient_detail']->birthdate)); ?></span>)</span>
                                    </div>
                                    <div class="d-flex align-items-center  justify-content-between flex-wrap">
                                        <!-- <p class="text-dark h7 me-3 mb-0"><svg height="20" fill="#d01337" style="    margin-top: -2px;" enable-background="new 0 0 24 24" id=fi_3596091 viewBox="0 0 24 24" xmlns=http://www.w3.org/2000/svg>
                                                <g>
                                                    <path d="m21.5 21h-19c-1.378 0-2.5-1.122-2.5-2.5v-13c0-1.378 1.122-2.5 2.5-2.5h19c1.378 0 2.5 1.122 2.5 2.5v13c0 1.378-1.122 2.5-2.5 2.5zm-19-17c-.827 0-1.5.673-1.5 1.5v13c0 .827.673 1.5 1.5 1.5h19c.827 0 1.5-.673 1.5-1.5v-13c0-.827-.673-1.5-1.5-1.5z"></path>
                                                </g>
                                                <g>
                                                    <path d="m7.5 12c-1.378 0-2.5-1.122-2.5-2.5s1.122-2.5 2.5-2.5 2.5 1.122 2.5 2.5-1.122 2.5-2.5 2.5zm0-4c-.827 0-1.5.673-1.5 1.5s.673 1.5 1.5 1.5 1.5-.673 1.5-1.5-.673-1.5-1.5-1.5z"></path>
                                                </g>
                                                <g>
                                                    <path d="m11.5 17c-.276 0-.5-.224-.5-.5v-1c0-.827-.673-1.5-1.5-1.5h-4c-.827 0-1.5.673-1.5 1.5v1c0 .276-.224.5-.5.5s-.5-.224-.5-.5v-1c0-1.378 1.122-2.5 2.5-2.5h4c1.378 0 2.5 1.122 2.5 2.5v1c0 .276-.224.5-.5.5z"></path>
                                                </g>
                                                <g>
                                                    <path d="m20.5 9h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z"></path>
                                                </g>
                                                <g>
                                                    <path d="m20.5 13h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z"></path>
                                                </g>
                                                <g>
                                                    <path d="m20.5 17h-6c-.276 0-.5-.224-.5-.5s.224-.5.5-.5h6c.276 0 .5.224.5.5s-.224.5-.5.5z"></path>
                                                </g>
                                            </svg>
                                            <span>mymd632037</span>
                                        </p> -->
                                        <p class=" text-dark h7 me-3 mb-0">
                                            <!--<span class="text-dark fw-bold">Phone:</span>-->
                                            <svg id="fi_159832" height="15" fill="#d01337"
                                                style="    margin-top: -3px;" version="1.1" viewBox="0 0 482.6 482.6"
                                                x="0px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" y="0px">
                                                <g>
                                                    <path d="M98.339,320.8c47.6,56.9,104.9,101.7,170.3,133.4c24.9,11.8,58.2,25.8,95.3,28.2c2.3,0.1,4.5,0.2,6.8,0.2
        c24.9,0,44.9-8.6,61.2-26.3c0.1-0.1,0.3-0.3,0.4-0.5c5.8-7,12.4-13.3,19.3-20c4.7-4.5,9.5-9.2,14.1-14
        c21.3-22.2,21.3-50.4-0.2-71.9l-60.1-60.1c-10.2-10.6-22.4-16.2-35.2-16.2c-12.8,0-25.1,5.6-35.6,16.1l-35.8,35.8
        c-3.3-1.9-6.7-3.6-9.9-5.2c-4-2-7.7-3.9-11-6c-32.6-20.7-62.2-47.7-90.5-82.4c-14.3-18.1-23.9-33.3-30.6-48.8
        c9.4-8.5,18.2-17.4,26.7-26.1c3-3.1,6.1-6.2,9.2-9.3c10.8-10.8,16.6-23.3,16.6-36s-5.7-25.2-16.6-36l-29.8-29.8
        c-3.5-3.5-6.8-6.9-10.2-10.4c-6.6-6.8-13.5-13.8-20.3-20.1c-10.3-10.1-22.4-15.4-35.2-15.4c-12.7,0-24.9,5.3-35.6,15.5l-37.4,37.4
        c-13.6,13.6-21.3,30.1-22.9,49.2c-1.9,23.9,2.5,49.3,13.9,80C32.739,229.6,59.139,273.7,98.339,320.8z M25.739,104.2
        c1.2-13.3,6.3-24.4,15.9-34l37.2-37.2c5.8-5.6,12.2-8.5,18.4-8.5c6.1,0,12.3,2.9,18,8.7c6.7,6.2,13,12.7,19.8,19.6
        c3.4,3.5,6.9,7,10.4,10.6l29.8,29.8c6.2,6.2,9.4,12.5,9.4,18.7s-3.2,12.5-9.4,18.7c-3.1,3.1-6.2,6.3-9.3,9.4
        c-9.3,9.4-18,18.3-27.6,26.8c-0.2,0.2-0.3,0.3-0.5,0.5c-8.3,8.3-7,16.2-5,22.2c0.1,0.3,0.2,0.5,0.3,0.8
        c7.7,18.5,18.4,36.1,35.1,57.1c30,37,61.6,65.7,96.4,87.8c4.3,2.8,8.9,5,13.2,7.2c4,2,7.7,3.9,11,6c0.4,0.2,0.7,0.4,1.1,0.6
        c3.3,1.7,6.5,2.5,9.7,2.5c8,0,13.2-5.1,14.9-6.8l37.4-37.4c5.8-5.8,12.1-8.9,18.3-8.9c7.6,0,13.8,4.7,17.7,8.9l60.3,60.2
        c12,12,11.9,25-0.3,37.7c-4.2,4.5-8.6,8.8-13.3,13.3c-7,6.8-14.3,13.8-20.9,21.7c-11.5,12.4-25.2,18.2-42.9,18.2
        c-1.7,0-3.5-0.1-5.2-0.2c-32.8-2.1-63.3-14.9-86.2-25.8c-62.2-30.1-116.8-72.8-162.1-127c-37.3-44.9-62.4-86.7-79-131.5
        C28.039,146.4,24.139,124.3,25.739,104.2z">
                                                    </path>
                                                </g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                                <g></g>
                                            </svg>
                                            <span><?php echo e($data['phone']); ?></span>
                                        </p>
                                        <!-- <p class="mb-1 text-gray">Age: </p> -->
                                        <span>
                                        </span>
                                        <?php if($service): ?>
                                            <p class="text-dark h7 mb-0">
                                                <svg height="18" width="20" style="margin-top: -2px;"
                                                    fill="#d01337" clip-rule="evenodd" fill-rule="evenodd"
                                                    id="fi_9720867" stroke-linejoin="round" stroke-miterlimit="2"
                                                    viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <g id="Icon">
                                                        <path
                                                            d="m7.035 8.641 3.996-4.58c.244-.28.598-.441.969-.441s.725.161.969.441l3.996 4.58 3.813-2.408c.427-.269.972-.264 1.394.014s.642.778.562 1.277l-1.812 11.322c-.141.884-.904 1.534-1.798 1.534h-14.248c-.894 0-1.657-.65-1.798-1.534l-1.812-11.322c-.08-.499.14-.999.562-1.277s.967-.283 1.394-.014zm-4.213-.886 1.737 10.854c.025.156.159.271.317.271h14.248c.158 0 .292-.115.317-.271l1.737-10.854-3.567 2.252c-.536.339-1.239.236-1.656-.241l-3.955-4.534-3.955 4.534c-.417.477-1.12.58-1.656.241l-3.567-2.252z">
                                                        </path>
                                                        <path
                                                            d="m20.037 15.129c.414 0 .75.336.75.75s-.336.75-.75.75h-16.074c-.414 0-.75-.336-.75-.75s.336-.75.75-.75z">
                                                        </path>
                                                    </g>
                                                </svg>

                                                <?php echo e($service); ?>: Active <span>
                                                </span>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <!--Form header ends here-->
                        </div>
                    </div>
                    <div class="card-body" data-select2-id="select2-data-13-l6y1">
                        <!--Form header starts here-->
                        <div class="alert alert-danger" id="msg_holder" style="display:none"></div>
                        <?php if(!array_intersect(['add_pharmacy_order_agent'], $permissionPage)): ?>
                            <div class="row">
                                <div class="col-md-7">
                                </div>
                                <div class="col-md-5">
                                    <div class="row justify-content-end">
                                        <div class="col-md-12 mb-4">
                                            <div class="d-flex flex-wrap flex-md-nowarp gap-2">
                                                <div
                                                    class="form-check col-md p-0 border border-light border-0 justify-content-end d-flex">
                                                    <input type="radio" class="btn-check districtclass is_med_cls"
                                                        name="medicinecta" id="medicinecta" value="1"
                                                        autocomplete="off" checked=""
                                                        onclick="meddivonoff(this.value)">
                                                    <label class="btn py-1   rounded shadow-sm btn-outline-primary"
                                                        for="medicinecta">With Medicine</label>
                                                </div>
                                                <div class="form-check col-md p-0 border border-light border-0">
                                                    <input type="radio" class="btn-check districtclass is_med_cls"
                                                        name="medicinecta" id="medicinecta1" value="2"
                                                        autocomplete="off" onclick="meddivonoff(this.value)">
                                                    <label
                                                        class="btn py-1 px-3 w-100  rounded shadow-sm btn-outline-primary"
                                                        for="medicinecta1">Without Medicine</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="row" data-select2-id="select2-data-12-o492">

                            <!--<meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>Get Current Location</title>
                            <p id="location-info"></p>
                            <iframe
                                id="gmap_canvas"
                                width="100%"
                                height="100%"
                                src="https://maps.google.com/maps?q=22.5752726,88.4333651&z=15&output=embed"
                                frameborder="0"
                                style="border:0"
                                allowfullscreen=""
                                aria-hidden="false"
                                tabindex="0">
                            </iframe> -->
                            <!-- <div class="col-md-3 form-group panel">
                                <div class="border-bottom pb-0 mb-3  d-flex">
                                <div class="fileupload fileupload-new" data-provides="fileupload">
                                    <div class="fileupload-new thumbnail img_class">
                                    <img style="height: 49px;" src="" id="img1" alt="" />
                                    </div>
                                    <label class="form-label fw-bold" for="city">Image Upload</label>
                                    <input type="file" id="change_image" class="form-control form-control-sm " aria-label="Large file input example" name="img_url" />

                                </div>
                                </div>
                            </div> -->
                            <!-- <div class="col-7 col-md-8 col-lg-7 prescription_select2">
                                <div class="form-floating floatingFrmRow ">
                                    <select class="form-select select2-multpl-custom medicine_search" value="0" id="select_medicine" name="select_medicine[]" aria-label="select example"></select>
                                    <label class="form-check-label radius-6 colorBlack bgColorRed-2 availableSlotsBtn font_14" for="date3">Select Medicine's</label>
                                </div>
                            </div> -->
                            <div class="col-md-3 form-group panel selct-modal4"
                                data-select2-id="select2-data-11-ixk0">
                                <div class="border-bottom pb-0 mb-3  d-flex">
                                    <label for="exampleInputEmail1" class="form-label "> Pharmacy</label>
                                </div>
                                <select id="clinic_id" name="clinic_id" class="select2-multpl-custom1 form-select"
                                    data-style="py-0">
                                    <option value="">Select</option>
                                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($row['id']); ?>"
                                            <?php echo e($id ? ($data['clinic_id'] == $row['id'] ? 'selected' : '') : ($data['agent_clinic_id'] == $row['id'] ? 'selected' : '')); ?>>
                                            <?php echo e($row['clinic_name']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>

                                <div class="mt-4 meddivonoff_div">
                                    <div class="payment_label border-bottom pb-0 mb-3">
                                        <label for="exampleInputEmail1" class="form-label">Upload Prescription</label>
                                    </div>
                                    <div class="">
                                        <input type="file" class="form-control form-control-sm "
                                            aria-label="Large file input example" name="files">
                                    </div>
                                    <span style="color: red; font-size: 12px;"> * Upload only jpg, jpeg, png,
                                        pdf.</span>
                                </div>
                                
                            </div>
                            <div class="col-md-9 ps-md-5 " id="medicineitemdiv"
                                data-select2-id="select2-data-medicineitemdiv">
                                <div class=" border-bottom pb-2 mb-3 ">
                                    <div class="col-md-12">
                                        <div class="row">
                                            <div class="col-md-7 col-7">
                                                <h6 class="h7">Items</h6>
                                            </div>
                                            <div class="col-md-3 col-3 text-center">
                                                <h6 class="h7 ">Quantity</h6>
                                            </div>
                                            <!-- <div class="col-md-2 ">
                                                <h6 class="h7">Report Delivery Date</h6>
                                            </div>
                                            <div class="col-md-1 ">
                                                <h6 class="h7">Charges</h6>
                                            </div>
                                            <div class="col-md-1 ">
                                                <h6 class="h7">Discount</h6>
                                            </div>
                                            <div class="col-md-1 ">
                                                <h6 class="h7">Total</h6>
                                            </div>
                                            <div class="col-md-1  text-center">
                                                <h6 class="h7">IsUrgent</h6>
                                            </div> -->
                                            <div class="col-md-2 col-2 px-0 px-md-3 text-center">
                                                <h6 class="h7">Action</h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row meddivonoff_div" data-select2-id="select2-data-49-6x8p">
                                    
                                    <div id="myDiv" class="col-md-12  "></div>
                                    <div id="medicine_box" class="col-md-12 mb-2">
                                        <div class="row" id="medicine_row-1"
                                            data-select2-id="select2-data-append_lav_div">
                                            <div class="col-md-7 col-7 mb-2 prescription_select2"
                                                data-select2-id="select2-data-48-ogys">
                                                <input type="hidden" id="medicine_price_1" value="0">
                                                <select id="medicine_id_1" name="medicine_ids[]" data-id="1"
                                                    class="select2-multpl-custom1 form-select medicine_ids" data-style="py-0" onchange="calcutaleTotalAmount()">
                                                    <option value="">Select</option>
                                                    <?php $__currentLoopData = $list['medicine_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($row['id']); ?>" data-price="<?php echo e($row['price']); ?>">
                                                            <?php echo e($row['name']); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <div class="col-md-3 col-3 mb-2 mb-md-0 ">
                                                <div class="d-flex justify-content-center align-items-center">
                                                    <input type="button" value="-"
                                                        class="button_minus button-minus border rounded-circle  icon-shape icon-sm mx-1 calcutaleTotalAmount"
                                                        data-field="quantity" style="width: 25px; height:25px;">
                                                    <input type="number"
                                                        class="col-md-3 form-control form-control-sm text-center pe-md-0 medquantity"
                                                        name="quantitys[]" id="quantity_1" value="1" min="1"
                                                        style="width: 50px;" placeholder="" readonly="">
                                                    <input type="button" value="+"
                                                        class="button_plus button-plus border rounded-circle icon-shape icon-sm mx-1 calcutaleTotalAmount"
                                                        data-field="quantity" style="width: 25px; height:25px;">
                                                </div>
                                            </div>
                                            <div
                                                class="col-md-2 col-2 d-flex justify-content-center align-items-center mb-2 mb-md-0">
                                                <button type="button" title="Remove" onclick="removeMedicine(1)"
                                                    class="btn btn-sm bg-transparent border-0 p-0">
                                                    <svg height="12pt" id="fi_1214428" viewBox="-40 0 427 427.00131"
                                                        width="12pt" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="m232.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0">
                                                        </path>
                                                        <path
                                                            d="m114.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0">
                                                        </path>
                                                        <path
                                                            d="m28.398438 127.121094v246.378906c0 14.5625 5.339843 28.238281 14.667968 38.050781 9.285156 9.839844 22.207032 15.425781 35.730469 15.449219h189.203125c13.527344-.023438 26.449219-5.609375 35.730469-15.449219 9.328125-9.8125 14.667969-23.488281 14.667969-38.050781v-246.378906c18.542968-4.921875 30.558593-22.835938 28.078124-41.863282-2.484374-19.023437-18.691406-33.253906-37.878906-33.257812h-51.199218v-12.5c.058593-10.511719-4.097657-20.605469-11.539063-28.03125-7.441406-7.421875-17.550781-11.5546875-28.0625-11.46875h-88.796875c-10.511719-.0859375-20.621094 4.046875-28.0625 11.46875-7.441406 7.425781-11.597656 17.519531-11.539062 28.03125v12.5h-51.199219c-19.1875.003906-35.394531 14.234375-37.878907 33.257812-2.480468 19.027344 9.535157 36.941407 28.078126 41.863282zm239.601562 279.878906h-189.203125c-17.097656 0-30.398437-14.6875-30.398437-33.5v-245.5h250v245.5c0 18.8125-13.300782 33.5-30.398438 33.5zm-158.601562-367.5c-.066407-5.207031 1.980468-10.21875 5.675781-13.894531 3.691406-3.675781 8.714843-5.695313 13.925781-5.605469h88.796875c5.210937-.089844 10.234375 1.929688 13.925781 5.605469 3.695313 3.671875 5.742188 8.6875 5.675782 13.894531v12.5h-128zm-71.199219 32.5h270.398437c9.941406 0 18 8.058594 18 18s-8.058594 18-18 18h-270.398437c-9.941407 0-18-8.058594-18-18s8.058593-18 18-18zm0 0">
                                                        </path>
                                                        <path
                                                            d="m173.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0">
                                                        </path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-start mb-4" id="medicineaddbutton">
                                        <div class="">
                                            <button type="button" id="addItemButton"
                                                class="btn btn-sm btn-primary text-white"
                                                onclick="addMoreMedicine()">Add
                                                Item</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-between">
                            <div class="mt-4 col-md-3 col-12">
                                <div class="payment_label ">
                                    <label for="exampleInputEmail1" class="form-label">Remarks</label>
                                </div>
                                <div class="">
                                    <textarea class="form-control form-control-sm " rows="1" cols="1" name="remarks" id="remarks"
                                        placeholder=" "></textarea>
                                </div>
                            </div>
                            <div class="mt-4 col-md-3 col-12 pe-none">
                                <div class="payment_label">
                                    <label for="exampleInputEmail1" class="form-label">Total Amount</label>
                                </div>
                                <div class="">
                                    <input class="form-control form-control-sm " rows="1" cols="1" name="total_amount" id="total_amount" placeholder="" value="0" readonly="">
                                </div>
                            </div>
                        </div>
                        <!--Form header ends here-->
                        <!---- Select Section Starts here ----->
                        <div class="row">
                            <div class="modal-footer">
                                <?php if($id): ?>
                                    <button type="submit" class="btn btn-primary text-white"  id="submitBtn">Update</button>
                                <?php else: ?>
                                    <button type="submit" name="submit" id="hiddenFormSubmit" style="display: none"></button>
                                    <button type="button" class="btn btn-primary text-white" onclick="sendOtpBeforeSubmit()" id="submitBtn">Submit</button>
                                <?php endif; ?>
                            </div>
                            <div class="form-group col-md-12">
                                <div id="errorMessage" class="" style="color: red;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!------------Top card ends here----------->
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <div class="form-group col-md-4">
                <label for="exampleInputEmail1" class="form-label fw-bold">Patient Name <span
                        class="text-primary">*</span></label>
                <p id="name" class="mt-2"><?php echo e($data['patient_detail']->name); ?></p>
            </div>

            <div class="form-group col-md-4">
                <label for="exampleInputEmail1" class="form-label fw-bold">Phone <span
                        class="text-primary">*</span></label>
                <p id="phone" class="mt-2"><?php echo e($data['phone']); ?></p>
            </div>

            <div class="form-group col-md-4 ">
                <label for="exampleInputEmail1" class="form-label fw-bold">Collection Type <span
                        class="text-primary">*</span></label>
                <div>
                    <select id="type_of_collection" name="type_of_collection"
                        class="select2-multpl-custom1 form-select" data-style="py-0"
                        onchange="collectionType(this.value)">
                        <option value="">Select</option>
                        <option value="HC"
                            <?php echo e($id ? ($data['order_detail']->type_of_collection == 'HC' ? 'selected' : '') : ''); ?>>
                            Home Delivery</option>
                        <option value="CV"
                            <?php echo e($id ? ($data['order_detail']->type_of_collection == 'CV' ? 'selected' : '') : ''); ?>>
                            Store Pickup</option>
                    </select>
                </div>
            </div>

            <div class="form-group col-md-4">
                <label for="exampleInputEmail1" class="form-label fw-bold">Collection status <span
                        class="text-primary">*</span></label>
                <div>
                    <select id="status" name="status" class="select2-multpl-custom1 form-select"
                        data-style="py-0">
                        <?php $__currentLoopData = $data['status_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key2 => $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key2); ?>"
                                <?php echo e($key2 == $data['order_detail']->status ? 'selected' : ''); ?>>
                                <?php echo e($row2); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>

            <div id="delivery_details"
                style="<?php echo e($data['order_detail']->type_of_collection == 'HC' ? '' : 'display: none'); ?>">
                <div class="row mt-4">
                    <div class="form-group col-md-4">
                        <label for="exampleInputBulding" class="form-label fw-bold">Building No.</label>
                        <input type="text" class="form-control form-control-sm" name="building_no"
                            id="building_no" value="<?php echo e($data['order_detail']->building_no); ?>" placeholder="">
                    </div>

                    <div class="form-group col-md-4">
                        <label for="exampleInputAddress" class="form-label fw-bold">Full Address</label>
                        <input type="text" class="form-control form-control-sm" name="full_address"
                            id="full_address" value="<?php echo e($data['order_detail']->full_address); ?>" placeholder="">
                    </div>

                    <div class="form-group col-md-4">
                        <label for="exampleInputLandmark" class="form-label fw-bold">Land Mark</label>
                        <input type="text" class="form-control form-control-sm" name="landmark" id="landmark"
                            value="<?php echo e($data['order_detail']->landmark); ?>" placeholder="">
                    </div>

                    <div class="form-group col-md-4">
                        <label for="exampleInputCity" class="form-label fw-bold">City</label>
                        <input type="text" class="form-control form-control-sm" name="city" id="city"
                            value="<?php echo e($data['order_detail']->city); ?>" placeholder="">
                    </div>

                    <div class="form-group col-md-4">
                        <label for="exampleInputPincode" class="form-label fw-bold">Pincode</label>
                        <input type="text" class="form-control form-control-sm" name="pincode" id="pincode"
                            value="<?php echo e($data['order_detail']->pincode); ?>" placeholder="">
                    </div>
                </div>
            </div>

            <div class="form-group col-md-4" id="clinic_show_div"
                style="<?php echo e($data['order_detail']->type_of_collection == 'CV' ? '' : 'display: none'); ?>">
                <label for="exampleInputEmail1" class="form-label fw-bold">Clinic</label>
                <select id="clinic_id" name="clinic_id" class="select2-multpl-custom1 form-select"
                    data-style="py-0">
                    <option value="">Select</option>
                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['id']); ?>"
                            <?php echo e($id ? ($data['order_detail']->clinic_id == $row['id'] ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['clinic_name']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12" id="medicineitemdiv" data-select2-id="select2-data-medicineitemdiv">
                <div class=" border-bottom pb-2 mb-3 ">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-7 col-7">
                                <h6 class="h7">Items</h6>
                            </div>
                            <div class="col-md-3 col-3 text-center">
                                <h6 class="h7 ">Quantity</h6>
                            </div>
                            <div class="col-md-2 col-2 px-0 px-md-3 text-center">
                                <h6 class="h7">Action</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row meddivonoff_div" data-select2-id="select2-data-49-6x8p">
                    <div id="myDiv" class="col-md-12  "></div>
                    <div id="medicine_box" class="col-md-12 mb-2">
                        <?php $__currentLoopData = $data['order_detail']->orderChildMedicines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="row" id="medicine_row-<?php echo e($key); ?>"
                                data-select2-id="select2-data-append_lav_div">
                                <div class="col-md-7 col-7 mb-2 prescription_select2"
                                    data-select2-id="select2-data-48-ogys">
                                    <input type="hidden" id="medicine_price_<?php echo e($key); ?>" value="0">
                                    <select id="medicine_id_<?php echo e($key); ?>" name="medicine_ids[]"
                                        class="select2-multpl-custom1 form-select medicine_ids" data-id="<?php echo e($key); ?>" data-style="py-0" onchange="calcutaleTotalAmount()">
                                        <option value="">Select</option>
                                        <?php $__currentLoopData = $list['medicine_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($row2['id']); ?>" data-price="<?php echo e($row2['price']); ?>"
                                                <?php echo e($row['medicine_id'] == $row2['id'] ? 'selected' : ''); ?>>
                                                <?php echo e($row2['name']); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="col-md-3 col-3 mb-2 mb-md-0 ">
                                    <div class="d-flex justify-content-center align-items-center">
                                        <input type="button" value="-"
                                            class="button_minus button-minus border rounded-circle  icon-shape icon-sm mx-1 "
                                            data-field="quantity" style="width: 25px; height:25px;">
                                        <input type="number"
                                            class="col-md-3 form-control form-control-sm text-center pe-md-0 medquantity"
                                            name="quantitys[]" id="quantity_<?php echo e($key); ?>" value="<?php echo e($row['quantity']); ?>" min="1"
                                            style="width: 50px;" placeholder="" readonly="">
                                        <input type="button" value="+"
                                            class="button_plus button-plus border rounded-circle icon-shape icon-sm mx-1 "
                                            data-field="quantity" style="width: 25px; height:25px;">
                                    </div>
                                </div>
                                <div
                                    class="col-md-2 col-2 d-flex justify-content-center align-items-center mb-2 mb-md-0">
                                    <button type="button" title="Remove"
                                        onclick="removeMedicine(<?php echo e($key); ?>)"
                                        class="btn btn-sm bg-transparent border-0 p-0">
                                        <svg height="12pt" id="fi_1214428" viewBox="-40 0 427 427.00131"
                                            width="12pt" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="m232.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0">
                                            </path>
                                            <path
                                                d="m114.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0">
                                            </path>
                                            <path
                                                d="m28.398438 127.121094v246.378906c0 14.5625 5.339843 28.238281 14.667968 38.050781 9.285156 9.839844 22.207032 15.425781 35.730469 15.449219h189.203125c13.527344-.023438 26.449219-5.609375 35.730469-15.449219 9.328125-9.8125 14.667969-23.488281 14.667969-38.050781v-246.378906c18.542968-4.921875 30.558593-22.835938 28.078124-41.863282-2.484374-19.023437-18.691406-33.253906-37.878906-33.257812h-51.199218v-12.5c.058593-10.511719-4.097657-20.605469-11.539063-28.03125-7.441406-7.421875-17.550781-11.5546875-28.0625-11.46875h-88.796875c-10.511719-.0859375-20.621094 4.046875-28.0625 11.46875-7.441406 7.425781-11.597656 17.519531-11.539062 28.03125v12.5h-51.199219c-19.1875.003906-35.394531 14.234375-37.878907 33.257812-2.480468 19.027344 9.535157 36.941407 28.078126 41.863282zm239.601562 279.878906h-189.203125c-17.097656 0-30.398437-14.6875-30.398437-33.5v-245.5h250v245.5c0 18.8125-13.300782 33.5-30.398438 33.5zm-158.601562-367.5c-.066407-5.207031 1.980468-10.21875 5.675781-13.894531 3.691406-3.675781 8.714843-5.695313 13.925781-5.605469h88.796875c5.210937-.089844 10.234375 1.929688 13.925781 5.605469 3.695313 3.671875 5.742188 8.6875 5.675782 13.894531v12.5h-128zm-71.199219 32.5h270.398437c9.941406 0 18 8.058594 18 18s-8.058594 18-18 18h-270.398437c-9.941407 0-18-8.058594-18-18s8.058593-18 18-18zm0 0">
                                            </path>
                                            <path
                                                d="m173.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0">
                                            </path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="d-flex justify-content-start mb-4" id="medicineaddbutton">
                        <div class="">
                            <button type="button" id="addItemButton" class="btn btn-sm btn-primary text-white"
                                onclick="addMoreMedicine()">Add
                                Item</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row justify-content-between">
            <div class="mt-4 col-md-3 col-12">
                <div class="payment_label ">
                    <label for="exampleInputEmail1" class="form-label">Remarks</label>
                </div>
                <div class="">
                    <textarea class="form-control form-control-sm " rows="1" cols="1" name="remarks" id="remarks"
                        placeholder=" "><?php echo e($data['order_detail']->remarks); ?></textarea>
                </div>
            </div>
            <div class="mt-4 col-md-3 col-12 pe-none">
                <div class="payment_label">
                    <label for="exampleInputEmail1" class="form-label">Total Amount</label>
                </div>
                <div class="">
                    <input class="form-control form-control-sm " rows="1" cols="1" name="total_amount" id="total_amount" placeholder="" value="<?php echo e(number_format($data['order_detail']->total_amount, 2)); ?>" readonly="">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="modal-footer">
                <?php if($id): ?>
                    <button type="submit" class="btn btn-primary text-white"  id="submitBtn">Update</button>
                <?php else: ?>
                    <button type="submit" name="submit" id="hiddenFormSubmit" style="display: none"></button>
                    <button type="button" class="btn btn-primary text-white" onclick="sendOtpBeforeSubmit()" id="submitBtn">Submit</button>
                <?php endif; ?>                
            </div>
            <div class="form-group col-md-12">
                <div id="errorMessage" class="" style="color: red;"></div>
            </div>
        </div>
    <?php endif; ?>
</form>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Pharmacy\resources/views/order/api/addEdit.blade.php ENDPATH**/ ?>