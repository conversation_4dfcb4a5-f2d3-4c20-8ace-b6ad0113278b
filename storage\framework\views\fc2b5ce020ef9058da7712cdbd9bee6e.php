<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td><?php echo e($row['family_id']); ?></td>
        <td>
            <?php echo e($row['name']); ?>

            <?php if(!isset($row['parent_id'])): ?>
                <span class="text-primary h8 fw-bold">(Parent)</span>
            <?php endif; ?>
        </td>
        
        <td><?php echo e($row['parent']['phone'] ?? $row['phone']); ?></td>
        
        <td>
            <div class="d-flex flex-nowrap gap-2">
                <?php if(array_intersect(['info_patient'], $permissionPage)): ?>
                    <a class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" title="Info" data-bs-toggle="modal" data-bs-target="#exampleInfoModal"
                        onclick="infoDetails('<?php echo e(config('patient.url') . 'detail/' . $row['id']); ?>','GET','#info-div-modal')">
                        <svg class="icon-16" xmlns="http://www.w3.org/2000/svg" width="32" viewBox="0 0 24 24"
                            fill="currentColor" height="32">
                            <path
                                d="M8 10.5378C8 9.43327 8.89543 8.53784 10 8.53784H11.3333C12.4379 8.53784 13.3333 9.43327 13.3333 10.5378V19.8285C13.3333 20.9331 14.2288 21.8285 15.3333 21.8285H16C16 21.8285 12.7624 23.323 10.6667 22.9361C10.1372 22.8384 9.52234 22.5913 9.01654 22.3553C8.37357 22.0553 8 21.3927 8 20.6832V10.5378Z"
                                fill="currentColor"></path>
                            <rect opacity="0.4" x="8" y="1" width="5" height="5" rx="2.5"
                                fill="currentColor"></rect>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['edit_patient'], $permissionPage)): ?>
                    <?php
                        if (array_intersect(['add_family_patient'], $permissionPage) && !isset($row['parent_id'])){
                            $edit_url = route('patient.addForm', ['id' => $row['id']]);
                        }else{
                            $edit_url = route('patient.addFamilyForm', ['id' => $row['parent_id'], 'mem_id' => $row['id']]);
                        }
                    ?>
                    <a href="<?php echo e($edit_url); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="Edit" role="button">
                        <svg width="32" class="icon-16" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.4"
                                d="M19.9927 18.9534H14.2984C13.7429 18.9534 13.291 19.4124 13.291 19.9767C13.291 20.5422 13.7429 21.0001 14.2984 21.0001H19.9927C20.5483 21.0001 21.0001 20.5422 21.0001 19.9767C21.0001 19.4124 20.5483 18.9534 19.9927 18.9534Z"
                                fill="currentColor"></path>
                            <path
                                d="M10.309 6.90385L15.7049 11.2639C15.835 11.3682 15.8573 11.5596 15.7557 11.6929L9.35874 20.0282C8.95662 20.5431 8.36402 20.8344 7.72908 20.8452L4.23696 20.8882C4.05071 20.8903 3.88775 20.7613 3.84542 20.5764L3.05175 17.1258C2.91419 16.4915 3.05175 15.8358 3.45388 15.3306L9.88256 6.95545C9.98627 6.82108 10.1778 6.79743 10.309 6.90385Z"
                                fill="currentColor"></path>
                            <path opacity="0.4"
                                d="M18.1208 8.66544L17.0806 9.96401C16.9758 10.0962 16.7874 10.1177 16.6573 10.0124C15.3927 8.98901 12.1545 6.36285 11.2561 5.63509C11.1249 5.52759 11.1069 5.33625 11.2127 5.20295L12.2159 3.95706C13.126 2.78534 14.7133 2.67784 15.9938 3.69906L17.4647 4.87078C18.0679 5.34377 18.47 5.96726 18.6076 6.62299C18.7663 7.3443 18.597 8.0527 18.1208 8.66544Z"
                                fill="currentColor"></path>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['change_status_patient'], $permissionPage)): ?>
                    <?php
                        $status = $row['status'] == 0 ? 1 : 0;
                    ?>
                    <a href="#" title="<?php echo e($row['status'] == 0 ? 'Deactive' : 'Active'); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" data-stat="<?php echo e($status); ?>"
                        onclick="updateStatus('<?php echo e(config('patient.url') . 'updateStatus/' . $row['id']); ?>','POST',this)">
                        <svg class=" text-primary" xmlns="http://www.w3.org/2000/svg" width="16" viewBox="0 0 24 24"
                            fill="currentColor">
                            <g>
                                <circle cx="12" cy="12" r="8"
                                    fill="<?php echo e($row['status'] == 0 ? '#F10F0F' : '#13B907'); ?>"></circle>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['add_family_patient'], $permissionPage) && !isset($row['parent_id'])): ?>
                    <a href="<?php echo e(route('patient.addFamilyForm', ['id' => $row['id']])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="Add Family Member" role="button">
                        <svg class="icon-16" width="16" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.4"
                                d="M21.101 9.58786H19.8979V8.41162C19.8979 7.90945 19.4952 7.5 18.999 7.5C18.5038 7.5 18.1 7.90945 18.1 8.41162V9.58786H16.899C16.4027 9.58786 16 9.99731 16 10.4995C16 11.0016 16.4027 11.4111 16.899 11.4111H18.1V12.5884C18.1 13.0906 18.5038 13.5 18.999 13.5C19.4952 13.5 19.8979 13.0906 19.8979 12.5884V11.4111H21.101C21.5962 11.4111 22 11.0016 22 10.4995C22 9.99731 21.5962 9.58786 21.101 9.58786Z"
                                fill="currentColor"></path>
                            <path
                                d="M9.5 15.0156C5.45422 15.0156 2 15.6625 2 18.2467C2 20.83 5.4332 21.5001 9.5 21.5001C13.5448 21.5001 17 20.8533 17 18.269C17 15.6848 13.5668 15.0156 9.5 15.0156Z"
                                fill="currentColor"></path>
                            <path opacity="0.4"
                                d="M9.50023 12.5542C12.2548 12.5542 14.4629 10.3177 14.4629 7.52761C14.4629 4.73754 12.2548 2.5 9.50023 2.5C6.74566 2.5 4.5376 4.73754 4.5376 7.52761C4.5376 10.3177 6.74566 12.5542 9.50023 12.5542Z"
                                fill="currentColor"></path>
                        </svg>
                    </a>
                <?php endif; ?>
            </div>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Patient\resources/views/patient/api/list.blade.php ENDPATH**/ ?>