<?php if(count($list['patients']) > 0): ?>
    <?php $__currentLoopData = $list['patients']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td><?php echo e($row->name); ?></td>

            <td><?php echo e($row->sex); ?></td>
            <td class="abwrap">
                <?php
                    $check_membership = 0;
                    $check_video_consult = 0;
                    $check_limit = 0;
                    $ab_clinic = 0;
                ?>
                <?php if(count($row->membershipRegistrations) > 0): ?>
                    <?php $__currentLoopData = $row->membershipRegistrations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($row2->is_renewal == 1 && in_array($row2->status, [3, 4, 6])): ?>
                            <div><?php echo e($row2->memberships->name); ?><br>
                                <?php if($row2->status == 3): ?>
                                    <?php
                                        $check_ab_tc = DB::table('appointments')
                                            ->where('patient_id', $row2->patient_id)
                                            ->where('appointment_type', 2)
                                            ->whereBetween('date', [$row2->start_date, $row2->end_date])
                                            ->count();
                                        if ($row2->card_type == 2) {
                                            $check_membership = 1;
                                            $check_video_consult = 1;
                                        }
                                        elseif ($row2->card_type == 1) {
                                            $ab_clinic = $row2->clinic_id;
                                            if ($check_ab_tc >= 2) {
                                                $check_limit = 1;
                                            }
                                            else {
                                                $check_membership = 1;
                                            }
                                            $check_video_consult = 1;
                                        }
                                    ?>
                                    Expiry Date: <?php echo e($row2->end_date); ?>

                                <?php elseif($row2->status == 4): ?>
                                    <span class="h7 py-1 px-2 rounded-1 bg-primary text-white">Expired</span>
                                <?php elseif($row2->status == 6): ?>
                                    <span class="h7 py-1 px-2 rounded-1 bg-primary text-white">Upcoming</span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </td>
            <td>
                <div class="d-flex flex-nowrap gap-1 justify-content-center">
                    <button class="btn btn-sm btn-primary d-flex gap-1 py-2 px-2 align-items-center"
                        onclick="addAppointment(<?php echo e($row->id); ?>,<?php echo e($phone); ?>)">Add Appointment</button>
                    <?php if($check_membership == 1): ?>
                        <button class="btn btn-sm btn-gray d-flex gap-1 py-2 px-2 align-items-center"
                            onclick="addTeleAppointment(<?php echo e($row->id); ?>,<?php echo e($phone); ?>,<?php echo e($ab_clinic); ?>)">Tele
                            Consultation</button>
                    <?php endif; ?>
                    <?php if($check_video_consult == 1): ?>
                        <button class="btn btn-sm btn-gray d-flex gap-1 py-2 px-2 align-items-center"
                            onclick="addVideoAppointment(<?php echo e($row->id); ?>,<?php echo e($phone); ?>,<?php echo e($ab_clinic); ?>)">Video
                            Consultation</button>
                    <?php endif; ?>
                    <?php if($check_limit == 1): ?>
                        <span class="badge rounded-pill bg-dark py-2 px-3 mt-2">Limit Reached</span>
                    <?php endif; ?>
                </div>
            </td>
        </tr>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php else: ?>
    <tr>
        <td colspan="5" class="border-0">
            No patients found
        </td>
    </tr>
<?php endif; ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/api/listFamily.blade.php ENDPATH**/ ?>