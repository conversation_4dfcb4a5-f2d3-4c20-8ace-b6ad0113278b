<?php

namespace Modules\Appointment\Services;

use App\Services\ApplicationDefaultService;
use Modules\Appointment\Models\Appointment;
use Modules\Appointment\Models\Clinic;
use Modules\Appointment\Models\Patient;
use Modules\Appointment\Models\Doctor;
use Modules\Appointment\Models\Schedule;
use Modules\Appointment\Models\MembershipRegistration;
use Modules\Reward\Models\RewardPoint;
use DB;
use Carbon\Carbon;

class AppointmentService extends ApplicationDefaultService
{
    public $entity;
    public $entityClinic;
    public $entityPatient;
    public $entityDoctor;
    public $entitySchedule;

    public $columns = [
        'appointments.id',
        'appointments.patient_id',
        'appointments.patient_phone',
        'appointments.doctor_id',
        'appointments.clinic_id',
        'appointments.date',
        'appointments.time_slot',
        'appointments.schedule_id',
        'appointments.remarks',
        'appointments.payment_status',
        'appointments.unique_bill_id',
        'appointments.unique_queue_number',
        'appointments.cancel_reason',
        'appointments.appointment_type',
        'appointments.hand_write_prescription',
        'appointments.e_prescription_upload_fairbase',
        'appointments.data_source',
        'appointments.is_exist',
        'appointments.executive_name',
        'appointments.executive_name_remarks',
        'appointments.meeting_id',
        'appointments.status',
        'appointments.created_by',
        'appointments.modified_by',
        'appointments.deleted_by',
        'appointments.created_at',
        'appointments.updated_at',
        'appointments.deleted_at'
    ];
    public $cacheColumns = [
        'id',
        'patient_id',
        'patient_phone',
        'doctor_id',
        'clinic_id',
        'date',
        'time_slot',
        'schedule_id',
        'remarks',
        'payment_status',
        'unique_bill_id',
        'unique_queue_number',
        'cancel_reason',
        'appointment_type',
        'hand_write_prescription',
        'e_prescription_upload_fairbase',
        'data_source',
        'is_exist',
        'executive_name',
        'executive_name_remarks',
        'meeting_id',
        'status',
        'created_by',
        'modified_by',
        'deleted_by',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function __construct(Appointment $entity,Clinic $entityClinic,Patient $entityPatient,Doctor $entityDoctor) {
        $this->entity =$entity;
        $this->entityClinic =$entityClinic;
        $this->entityPatient =$entityPatient;
        $this->entityDoctor =$entityDoctor;
    }
    public function allAppointments(){
        $this->entity = $this->entity->select($this->columns)
            // ->where('status',1)
            ->get();
        return $this->entity;
    }
    public function patientAppointments($patient_id){
        $this->entity = $this->entity->select('date','time_slot','status','clinic_id')
            ->where('patient_id',$patient_id)
            ->with('clinics:id,clinic_name')
            ->orderBy('date', 'desc')
            ->get();
        return $this->entity;
    }
    public function allClinics(){
        $this->entityClinic = $this->entityClinic->select('id','clinic_name')
            ->where('status',1)
            ->get();
        return $this->entityClinic;
    }
    public function allDoctors(){
        $this->entityDoctor = $this->entityDoctor->select('id','user_id','doctor_type')
            ->where('status',1)
            ->with('user:id,username')
            ->get();
        return $this->entityDoctor;
    }
    public function getPatientID($phone){
        $patient_id = Patient::where('phone',$phone)->value('id');
        return $patient_id;
    }
    public function getFamilys($phone){
        $family_id = $this->entityPatient->where('phone',$phone)->value('family_id');
        $this->entityPatient = $this->entityPatient->select('id','family_id','name','sex')
            ->where('family_id',$family_id)
            ->with('membershipRegistrations')
            ->get();
        return $this->entityPatient;
    }
    public function findPatient($patient_id){
        $this->entityPatient = $this->entityPatient->select('id','family_id','name')->find($patient_id);
        return $this->entityPatient;
    }
    public function getSlots($data){
        $this->entitySchedule = new Schedule;
        if (isset($data['select']) && !empty($data['select'])) {
            $this->entitySchedule = $this->entitySchedule->selectRaw($data["select"]);
        }
        $this->entitySchedule = $this->entitySchedule->where('status',1);
        if (isset($data['filter']) && !empty($data['filter'])) {
            foreach ($data['filter'] as $column => $value) {
                $this->entitySchedule = $this->entitySchedule->where($column, $value);
            }
        }
        if (isset($data['date_filter']) && !empty($data['date_filter'])) {
            $this->entitySchedule = $this->entitySchedule->where('date', '>=', $data['date_filter']);
        }
        if (isset($data['groupby']) && !empty($data['groupby'])) {
            $this->entitySchedule = $this->entitySchedule->groupBy($data["groupby"]);
        }
        $this->entitySchedule = $this->entitySchedule->orderBy('date','asc');
        $this->entitySchedule = $this->entitySchedule->get()->toArray();
        if (isset($data['collect']) && !empty($data['collect'])) {
            foreach ($data['collect'] as $dataSet => $set) {
                $this->entitySchedule = collect($this->entitySchedule)->map(function ($collectData) use ($set, $dataSet) {
                    $sub_data = DB::table($set['table']);
                    $sub_data = $sub_data->where($set['condition'], $collectData[$set['jsonValue']]);
                    $sub_data = $sub_data->value($set['select']);
                    return collect($collectData)
                        ->put($dataSet, $sub_data)
                        ->toArray();
                });
            }
        }
        return $this->entitySchedule;
    }
    public function getSlotsAssignDoctor(){
        $this->entitySchedule = new Schedule;
        $this->entitySchedule = $this->entitySchedule->select('id','doctor_id','clinic_id','date','is_time');

        $this->entitySchedule = $this->entitySchedule->where('date', date('Y-m-d'));
        $this->entitySchedule = $this->entitySchedule->groupBy('id','doctor_id','clinic_id','date','is_time');
        $this->entitySchedule = $this->entitySchedule->whereHas('users.appointmentDoctors', function($query) {
            $query->where('doctor_type', '!=', 2);
        });
        $this->entitySchedule = $this->entitySchedule->with('clinics','users','users.appointmentDoctors');
        $this->entitySchedule = $this->entitySchedule->get()->toArray();
        // dd($this->entitySchedule);
        $this->entitySchedule = collect($this->entitySchedule)->map(function ($collectData) {
            $sub_data = $this->checkEngaged($collectData['id']);
            return collect($collectData)
                ->put('checkEngaged', $sub_data)
                ->toArray();
        });
        return $this->entitySchedule;
    }
    public function checkAppointment(){
        $data = Appointment::where('patient_id',$this->request['patient_id']);
        $data = $data->where('schedule_id',$this->request['schedule_id']);
        $data = $data->where('status','!=',6);
        // $data = $data->where('clinic_id',$this->request['clinic_id']);
        // $data = $data->where('date',$this->request['date']);
        // $data = $data->where('time_slot',$this->request['time_slot']);
        $data = $data->count();
        return $data;
    }
    public function checkEngaged($schedule_id){
        $id = $this->entity->id;
        // dd($id);
        $data = Appointment::where('schedule_id',$schedule_id);
        $data = $data->where('id','!=',$id);
        $data = $data->where('status',4);
        $data = $data->count();
        return $data;
    }
    public function activeMembership($patient_id){
        $data = MembershipRegistration::where('patient_id',$patient_id)->select('card_type','end_date','category_id','clinic_id','registration_no');
        $data = $data->where('status',3);
        $data = $data->where('category_id',1);
        $data = $data->with('memberships:id,name');
        $data = $data->with('clinics:id,clinic_name');
        $data = $data->get()->toArray();
        return $data;
    }
    public function queueIncrementId($schedule_id)
    {
        $max_queue_number = DB::table('appointments')->where(['schedule_id' => $schedule_id])->max('unique_queue_number');
        if ($max_queue_number) {
            $max_queue_number += 1;
        }
        else {
            $max_queue_number = 1;
        }
        return $max_queue_number;
    }
    public function checkPreviousPrescriptions(){
        $this->entity['data'] = collect($this->entity['data'])->map(function ($collectData) {
            $sub_data = DB::table('prescriptions')->selectRaw('id');
            $sub_data = $sub_data->where(['patient_id' => $collectData['patient_id'],'doctor_id' => $collectData['doctor_id']]);
            $sub_data = $sub_data->where('appointment_id', '!=', $collectData['id']);
            $sub_data = $sub_data->where('date', '<', Carbon::today());
            $sub_data = $sub_data->orderBy('date', 'desc')->get();
            return collect($collectData)
                ->put('previous_prescription_id', (count($sub_data) > 0 ? $sub_data[0]->id : ''))
                ->toArray();
        });
        $this->rows = ['rows' => $this->entity['data'], 'totalCount' => $this->entity['total']];
        return $this->rows;
    }
    public function dataMigrate($contentInsert){
        return DB::transaction(function () use ($contentInsert) {
            Appointment::upsert(
                $contentInsert,
                ['id'], // Unique columns to check for duplicates
                $this->columns // Columns to update if a duplicate is found
            );
            DB::table('temp_increment_migrations')
                ->where('table_name', 'appointments')
                ->increment('count');
        });
    }
}
