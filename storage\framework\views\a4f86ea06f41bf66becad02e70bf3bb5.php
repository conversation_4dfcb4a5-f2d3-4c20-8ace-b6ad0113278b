<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td>
            <a href="<?php echo e(route('diagnostic.labTestStatus',[base64_encode($row['id'])])); ?>"><?php echo e($row['id']); ?></a>
        </td>
        <td><?php echo e($row['unique_id']); ?></td>
        <td>
            <?php echo e($row['patient_name']); ?>

            <br>
            <?php echo e(Helper::maskPhoneNumber($row['patient_phone'])); ?>

        </td>
        <td><?php echo e($row['doctor_name']); ?></td>
        <td><?php echo e($row['clinic_name']); ?></td>
        <td><?php echo e($row['date_of_collection']); ?></td>
        <td><?php echo e($row['type_of_collection']); ?></td>
        <td>
            <?php echo e($row['total_amount_sum']); ?>

        </td>
        <td>
            <?php echo e($row['due_amount_sum']); ?>

        </td>
        <td>
            <?php if(isset($row['assign_phlebotomists'])): ?>
                <?php echo e($row['assign_phlebotomists']['user_phlebotomists']['username'] ?? ''); ?>

                <br>
                <?php echo e(date('Y-m-d', strtotime($row['assign_phlebotomists']['created_at'] ?? ''))); ?>

                <br>
                <?php echo e($row['assign_phlebotomists']['schedule_time'] ?? ''); ?>

                <?php if($row['assign_phlebotomists']['remarks'] != null): ?>
                    <br>
                    Remarks : <?php echo e($row['assign_phlebotomists']['remarks'] ?? ''); ?>

                <?php endif; ?>
            <?php endif; ?>
        </td>
        <td>
            <?php
                $reported_status = false;
            ?>
            <?php if(count($row['diagnostic_breakup_reported_test']) > 0): ?>
                <?php
                    $reported_status = true;
                ?>
                <?php if(count($row['diagnostic_breakup_item']) == count($row['diagnostic_breakup_reported_test'])): ?>
                    Fully Reported
                <?php else: ?>
                    Partially Reported
                <?php endif; ?>
            <?php else: ?>
                <?php if(array_intersect(['change_status_diagnostic'], $permissionPage) && in_array($row['status'], [0, 1])): ?>
                    <select class="form-select form-select-sm m-bot15 pe-4" style="min-width:130px;" name="status"
                        onchange="updateDiagnosticStatus('<?php echo e(config('diagnostic.url') . 'updateStatus/' . $row['id']); ?>','POST',this)">
                        <?php $__currentLoopData = Arr::except($status_list, [2, 3, 4, 5, 6, 7, 8, 9]); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key2 => $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key2); ?>" <?php echo e($key2 == $row['status'] ? 'selected' : ''); ?>>
                                <?php echo e($row2); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                <?php else: ?>
                    <?php echo e($status_list[$row['status']]); ?>

                <?php endif; ?>
            <?php endif; ?>
        </td>
        <td>
            <div class="d-flex flex-nowrap gap-1">
                <?php if(array_intersect(['info_diagnostic'], $permissionPage)): ?>
                    <div class="table-content">
                        <div class="tooltip1">
                            <div class="tr" onclick="toggleTooltip(this)">
                                <div
                                    class="td btn btn-light mt-0 icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center">
                                    <svg clip-rule=evenodd fill-rule=evenodd height=20 id=fi_11524741
                                        image-rendering=optimizeQuality shape-rendering=geometricPrecision
                                        text-rendering=geometricPrecision viewBox="0 0 14872 14872"width=20
                                        xmlns=http://www.w3.org/2000/svg>
                                        <g id=Layer_x0020_1>
                                            <g id=_959695072>
                                                <path
                                                    d="m0 0h10896v1147c-194 9-386 32-572 66v-640h-9751v13726h9751v-754h572v1327h-10896zm10896 9746v1673h-572v-1739c186 34 378 57 572 66z"
                                                    fill=#333></path>
                                                <path
                                                    d="m12361 11959h-528v1046h528c288 0 523-235 523-523 0-288-235-523-523-523zm-7364 0h6459v1046h-6459zm-338 0-1055 399h-426c-69 0-124 56-124 124 0 68 55 124 124 124h426l1055 399v-523z"
                                                    fill=#04599c></path>
                                            </g>
                                            <path
                                                d="m11110 1685c2078 0 3762 1684 3762 3761 0 2078-1684 3762-3762 3762-406 0-797-65-1164-184-396 360-1050 681-1870 569 0 0 565-491 838-1093-948-683-1565-1796-1565-3054 0-2077 1684-3761 3761-3761zm0 2433c341 0 618-277 618-618s-277-617-618-617c-340 0-617 276-617 617s277 618 617 618zm-348 3805h697c128 0 232-105 232-233v-2741c0-128-104-232-232-232h-697c-128 0-232 104-232 232v2741c0 128 104 233 232 233z"
                                                fill=#04599c></path>
                                            <g fill=#333>
                                                <path d="m4096 1866h3447v697h-3447z"></path>
                                                <path d="m4096 3319h2576v697h-2576z"></path>
                                                <path d="m4096 6840h2816v697h-2816z"></path>
                                                <path d="m4096 8293h2576v697h-2576z"></path>
                                            </g>
                                            <path
                                                d="m1392 2794 677 462 1071-1540 476 331-1234 1776-164 236-238-162-915-625z"
                                                fill=#04599c fill-rule=nonzero></path>
                                            <path
                                                d="m1392 7651 677 463 1071-1541 476 332-1234 1776-164 236-238-162-915-625z"
                                                fill=#04599c fill-rule=nonzero></path>
                                        </g>
                                        <g id=boxes>
                                            <path d="m0 0h14872v14872h-14872z" fill=none></path>
                                        </g>
                                    </svg>

                                    <span class="tooltiptext">
                                        <span class="d-flex gap-2 "
                                            style="border-bottom: 1px solid #c2c2c2; background-color:#343434; padding:8px;     border-top-left-radius: 6px;     border-top-right-radius: 6px;">
                                            <span class="w-25">Tests:</span>
                                            <p class="mb-0" style="white-space: normal;">
                                                <?php echo e(implode(', ', Arr::pluck($row['tests'], 'test.test_name')) ?? ''); ?>

                                            </p>
                                        </span>
                                        <span class="d-flex gap-2"
                                            style="padding:8px;background-color: #626262;border-bottom: 1px solid #c2c2c2; border-top-left-radius: 6px;     border-top-right-radius: 6px;">
                                            <span class="w-25">Address:</span>
                                            <p class="mb-0 w-75" style="white-space: normal;">
                                                <?php echo e($row['full_address']); ?><br>
                                                <?php echo e($row['landmark']); ?><br>
                                                <?php echo e($row['city']); ?></br>
                                                <?php echo e($row['pincode']); ?>

                                            </p>
                                        </span>
                                        <span class="d-flex gap-2"
                                            style="padding:8px;background-color:#343434; border-bottom-left-radius: 6px;     border-bottom-right-radius: 6px;">
                                            <span class="w-25">Data Source:</span>
                                            <p class="mb-0 w-75" style="white-space: normal;">
                                                <?php echo e($row['appointment_type'] == 1 ? 'walk-in' : 'Campaign'); ?></p>
                                        </span>

                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if(array_intersect(['payment_settlement_diagnostic'], $permissionPage) && $row['due_amount_sum'] > 0): ?>
                    <a title="Payment Settlement"
                        class="btn btn-light mt-0 icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" href="<?php echo e(route('diagnostic.paymentSettlement', ['id' => $row['id']])); ?>">
                        <svg enable-background="new 0 0 512 512"height=20 id=fi_4773546 viewBox="0 0 512 512"width=20
                            xmlns=http://www.w3.org/2000/svg>
                            <g>
                                <path
                                    d="m512 75v-30c0-24.813-20.187-45-45-45h-422c-24.813 0-45 20.187-45 45v30l13 34-13 26v180c0 24.813 20.187 45 45 45h452c8.284 0 15-6.716 15-15v-210l-19-24z"
                                    fill=#0a789b></path>
                                <path
                                    d="m467 0h-211v360h241c8.284 0 15-6.716 15-15v-210l-19-24 19-36v-30c0-24.813-20.187-45-45-45z"
                                    fill=#08475e></path>
                                <path d="m0 75h512v60h-512z" fill=#ffe278></path>
                                <path d="m256 75h256v60h-256z" fill=#ffc178></path>
                                <g>
                                    <g>
                                        <g>
                                            <path
                                                d="m115 230h-40c-8.284 0-15-6.716-15-15s6.716-15 15-15h40c8.284 0 15 6.716 15 15s-6.716 15-15 15z"
                                                fill=#fff5f5></path>
                                        </g>
                                        <g>
                                            <path
                                                d="m241 290h-166c-8.284 0-15-6.716-15-15s6.716-15 15-15h166c8.284 0 15 6.716 15 15s-6.716 15-15 15z"
                                                fill=#fff5f5></path>
                                        </g>
                                        <g>
                                            <path
                                                d="m215 230h-40c-8.284 0-15-6.716-15-15s6.716-15 15-15h40c8.284 0 15 6.716 15 15s-6.716 15-15 15z"
                                                fill=#fff5f5></path>
                                        </g>
                                    </g>
                                </g>
                                <g>
                                    <g>
                                        <g>
                                            <g>
                                                <g>
                                                    <g>
                                                        <g>
                                                            <g>
                                                                <g>
                                                                    <g>
                                                                        <path
                                                                            d="m407 512c-2.076 0-4.153-.431-6.092-1.293-60.085-26.704-98.908-86.444-98.908-152.195v-61.512c0-5.928 3.491-11.3 8.908-13.707 63.762-28.339 128.422-28.339 192.184 0 5.417 2.407 8.908 7.779 8.908 13.707v61.512c0 65.751-38.823 125.491-98.908 152.195-1.939.862-4.016 1.293-6.092 1.293z"
                                                                            fill=#a2e786></path>
                                                                        <g>
                                                                            <path
                                                                                d="m503.092 283.293c-31.881-14.169-63.986-21.254-96.092-21.254v249.961c2.076 0 4.153-.431 6.092-1.293 60.085-26.704 98.908-86.444 98.908-152.195v-61.512c0-5.928-3.491-11.3-8.908-13.707z"
                                                                                fill=#00cb75></path>
                                                                        </g>
                                                                        <path
                                                                            d="m332 306.951v51.561c0 51.65 29.224 98.778 75 121.892 45.776-23.113 75-70.241 75-121.892v-51.561c-50.373-20.085-99.627-20.085-150 0z"
                                                                            fill=#fff5f5></path>
                                                                        <path
                                                                            d="m482 358.512v-51.561c-25.187-10.042-50.093-15.064-75-15.064v188.516c45.776-23.113 75-70.241 75-121.891z"
                                                                            fill=#e2dff4></path>
                                                                        <g>
                                                                            <path
                                                                                d="m385.787 418.82-21.213-21.214c-5.858-5.858-5.858-15.355 0-21.213 5.858-5.858 15.355-5.858 21.213 0l10.607 10.607 31.819-31.82c5.858-5.858 15.355-5.858 21.213 0 5.858 5.858 5.858 15.355 0 21.213l-42.426 42.427c-5.858 5.858-15.355 5.858-21.213 0z"
                                                                                fill=#0a789b></path>
                                                                        </g>
                                                                        <path
                                                                            d="m449.426 355.18c-5.858-5.858-15.355-5.858-21.213 0l-21.213 21.213v42.427l42.426-42.427c5.858-5.858 5.858-15.356 0-21.213z"
                                                                            fill=#08475e></path>
                                                                    </g>
                                                                </g>
                                                            </g>
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['refund_settlement_diagnostic'], $permissionPage) && $row['due_amount_sum'] < 0): ?>
                    <a title="Refund Settlement"
                        class="btn btn-light mt-0 icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" href="<?php echo e(route('diagnostic.refundSettlement', ['id' => $row['id']])); ?>">
                        <svg enable-background="new 0 0 512 512"height=20 id=fi_4773546 viewBox="0 0 512 512"width=20
                            xmlns=http://www.w3.org/2000/svg>
                            <g>
                                <path
                                    d="m512 75v-30c0-24.813-20.187-45-45-45h-422c-24.813 0-45 20.187-45 45v30l13 34-13 26v180c0 24.813 20.187 45 45 45h452c8.284 0 15-6.716 15-15v-210l-19-24z"
                                    fill=#0a789b></path>
                                <path
                                    d="m467 0h-211v360h241c8.284 0 15-6.716 15-15v-210l-19-24 19-36v-30c0-24.813-20.187-45-45-45z"
                                    fill=#08475e></path>
                                <path d="m0 75h512v60h-512z" fill=#ffe278></path>
                                <path d="m256 75h256v60h-256z" fill=#ffc178></path>
                                <g>
                                    <g>
                                        <g>
                                            <path
                                                d="m115 230h-40c-8.284 0-15-6.716-15-15s6.716-15 15-15h40c8.284 0 15 6.716 15 15s-6.716 15-15 15z"
                                                fill=#fff5f5></path>
                                        </g>
                                        <g>
                                            <path
                                                d="m241 290h-166c-8.284 0-15-6.716-15-15s6.716-15 15-15h166c8.284 0 15 6.716 15 15s-6.716 15-15 15z"
                                                fill=#fff5f5></path>
                                        </g>
                                        <g>
                                            <path
                                                d="m215 230h-40c-8.284 0-15-6.716-15-15s6.716-15 15-15h40c8.284 0 15 6.716 15 15s-6.716 15-15 15z"
                                                fill=#fff5f5></path>
                                        </g>
                                    </g>
                                </g>
                                <g>
                                    <g>
                                        <g>
                                            <g>
                                                <g>
                                                    <g>
                                                        <g>
                                                            <g>
                                                                <g>
                                                                    <g>
                                                                        <path
                                                                            d="m407 512c-2.076 0-4.153-.431-6.092-1.293-60.085-26.704-98.908-86.444-98.908-152.195v-61.512c0-5.928 3.491-11.3 8.908-13.707 63.762-28.339 128.422-28.339 192.184 0 5.417 2.407 8.908 7.779 8.908 13.707v61.512c0 65.751-38.823 125.491-98.908 152.195-1.939.862-4.016 1.293-6.092 1.293z"
                                                                            fill=#a2e786></path>
                                                                        <g>
                                                                            <path
                                                                                d="m503.092 283.293c-31.881-14.169-63.986-21.254-96.092-21.254v249.961c2.076 0 4.153-.431 6.092-1.293 60.085-26.704 98.908-86.444 98.908-152.195v-61.512c0-5.928-3.491-11.3-8.908-13.707z"
                                                                                fill=#00cb75></path>
                                                                        </g>
                                                                        <path
                                                                            d="m332 306.951v51.561c0 51.65 29.224 98.778 75 121.892 45.776-23.113 75-70.241 75-121.892v-51.561c-50.373-20.085-99.627-20.085-150 0z"
                                                                            fill=#fff5f5></path>
                                                                        <path
                                                                            d="m482 358.512v-51.561c-25.187-10.042-50.093-15.064-75-15.064v188.516c45.776-23.113 75-70.241 75-121.891z"
                                                                            fill=#e2dff4></path>
                                                                        <g>
                                                                            <path
                                                                                d="m385.787 418.82-21.213-21.214c-5.858-5.858-5.858-15.355 0-21.213 5.858-5.858 15.355-5.858 21.213 0l10.607 10.607 31.819-31.82c5.858-5.858 15.355-5.858 21.213 0 5.858 5.858 5.858 15.355 0 21.213l-42.426 42.427c-5.858 5.858-15.355 5.858-21.213 0z"
                                                                                fill=#0a789b></path>
                                                                        </g>
                                                                        <path
                                                                            d="m449.426 355.18c-5.858-5.858-15.355-5.858-21.213 0l-21.213 21.213v42.427l42.426-42.427c5.858-5.858 5.858-15.356 0-21.213z"
                                                                            fill=#08475e></path>
                                                                    </g>
                                                                </g>
                                                            </g>
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['invoice_diagnostic'], $permissionPage)): ?>
                    <a target="_blank" title="Diagnostic Invoice"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center "
                        role="button" href="<?php echo e(route('diagnostic.viewInvoice', $row['id'])); ?>">
                        <svg data-name="Filled Line"height=20 id=fi_16963082 viewBox="0 0 64 64"width=20
                            xmlns=http://www.w3.org/2000/svg>
                            <g>
                                <path
                                    d="m53 51.9762v-46.233c0-.7723-.8382-1.2531-1.5048-.8632l-3.1204 1.8248c-.3118.1824-.6978.1824-1.0096 0l-4.1204-2.4096c-.3118-.1824-.6978-.1824-1.0096 0l-4.1199 2.4093c-.3121.1825-.6984.1824-1.0104-.0004l-4.1094-2.4079c-.3122-.1829-.6989-.1829-1.0111 0l-4.1089 2.4076c-.3122.1829-.6989.1829-1.0111 0l-4.1094-2.4079c-.3119-.1828-.6983-.1829-1.0104-.0004l-4.1194 2.409c-.3121.1825-.6984.1824-1.0104-.0004l-3.1094-1.8219c-.6666-.3906-1.5055.0901-1.5055.8628v38.255h-4v16h36.9762l8.0238-8.0238z"
                                    fill=#eceff1></path>
                                <circle cx=43 cy=43 fill=#388e3c r=17></circle>
                                <path d="m4 44h8v12c0 2.2077-1.7923 4-4 4-2.2077 0-4-1.7923-4-4z" fill=#cfd8dc></path>
                            </g>
                            <g fill=#030611>
                                <path
                                    d="m12 19.4976c.4141 0 .75-.3359.75-.75v-1.5674c0-.4141-.3359-.75-.75-.75s-.75.3359-.75.75v1.5674c0 .4141.3359.75.75.75z">
                                </path>
                                <path
                                    d="m18.5 22.75h28c.4141 0 .75-.3359.75-.75s-.3359-.75-.75-.75h-28c-.4141 0-.75.3359-.75.75s.3359.75.75.75z">
                                </path>
                                <path
                                    d="m18.5 15.75h28c.4141 0 .75-.3359.75-.75s-.3359-.75-.75-.75h-28c-.4141 0-.75.3359-.75.75s.3359.75.75.75z">
                                </path>
                                <path
                                    d="m59.7432 37.0938c-.1377-.3911-.5684-.5947-.9561-.458-.3906.1382-.5957.5664-.458.957.6113 1.7334.9209 3.5527.9209 5.4072 0 8.9604-7.29 16.25-16.25 16.25s-16.25-7.2896-16.25-16.25 7.29-16.25 16.25-16.25c5.1699 0 10.0791 2.4961 13.1328 6.6772.2432.334.7109.4072 1.0479.1631.334-.2441.4072-.7134.1631-1.0479-1.0232-1.4014-2.2388-2.6275-3.5938-3.6567v-23.1455c0-.6348-.3301-1.2017-.8818-1.5171-.5488-.314-1.2031-.3105-1.7451.0083l-3.1445 1.8291c-.0693.0425-.1475.043-.2314-.0088l-4.1152-2.3916c-.5518-.3374-1.2334-.3369-1.7695-.0083l-4.125 2.4033c-.084.0498-.1689.0508-.2588-.002l-4.0957-2.3984c-.5591-.3315-1.2271-.332-1.7817-.0024l-4.104 2.4028c-.085.0508-.1699.0508-.2583-.0024l-4.0962-2.3979c-.5581-.332-1.2261-.3325-1.7808-.0024l-4.1147 2.4028c-.0845.0503-.1699.0503-.2573-.002l-3.103-1.8218c-.5513-.3198-1.2095-.3232-1.7603-.0044-.5488.3154-.8765.8813-.8765 1.5132v8.3994c0 .4141.3359.75.75.75s.75-.3359.75-.75v-8.3994c0-.123.0781-.1865.1245-.2129.0474-.0283.1455-.0654.2554-.0005l3.0977 1.8184c.5586.3306 1.2251.332 1.7808.0024l4.1147-2.4028c.0845-.0503.1694-.0503.2578.0024l4.0967 2.3979c.5581.3315 1.2271.3311 1.7812.0024l4.104-2.4028c.0859-.0508.1694-.0498.2583.002l4.0957 2.3984c.5596.3315 1.2275.3315 1.7812.0029l4.1348-2.4092c.0674-.042.1465-.042.2305.0093l4.1143 2.3911c.5498.3369 1.2324.3369 1.7695.0093l3.1338-1.8228c.1055-.062.1982-.0259.2432 0 .0469.0269.126.0903.126.2144v22.1272c-2.756-1.6823-5.9526-2.6174-9.25-2.6174-3.6458 0-7.0366 1.1075-9.8588 3h-14.6412c-.4141 0-.75.3359-.75.75s.3359.75.75.75h12.73c-1.7278 1.5366-3.1494 3.401-4.1741 5.5h-8.556c-.4141 0-.75.3359-.75.75s.3359.75.75.75h7.9078c-.6515 1.7234-1.0387 3.5708-1.1199 5.5h-6.788c-.4141 0-.75.3359-.75.75s.3359.75.75.75h6.788c.0812 1.9292.4684 3.7766 1.1199 5.5h-7.9078c-.4141 0-.75.3359-.75.75s.3359.75.75.75h8.556c1.8426 3.7746 4.9721 6.8046 8.8282 8.5h-24.4387c.803-.851 1.3043-1.9903 1.3043-3.25v-34.3773c0-.4141-.3359-.75-.75-.75s-.75.3359-.75.75v21.6274h-7.25c-.4141 0-.75.3359-.75.75v12c0 2.6191 2.1309 4.75 4.75 4.75h35c9.7871 0 17.75-7.9624 17.75-17.75 0-2.0249-.3389-4.0122-1.0068-5.9062zm-54.9932 18.9062v-11.25h6.5v11.25c0 1.792-1.458 3.25-3.25 3.25s-3.25-1.458-3.25-3.25z">
                                </path>
                                <path
                                    d="m51.6621 36.813-10.6064 10.6064c-.0977.0977-.2559.0977-.3535 0l-6.3643-6.3638c-.293-.293-.7676-.293-1.0605 0s-.293.7676 0 1.0605l6.3643 6.3638c.3408.3413.7891.5117 1.2373.5117s.8965-.1704 1.2373-.5117l10.6064-10.6064c.293-.293.293-.7676 0-1.0605s-.7676-.293-1.0605 0z">
                                </path>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['trf_diagnostic'], $permissionPage)): ?>
                    <a target="_blank" title="TRF"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center "
                        role="button" href="<?php echo e(route('diagnostic.viewTRF', $row['id'])); ?>">
                        <svg enable-background="new 0 0 468 468"height=20 id=fi_3082854 viewBox="0 0 468 468"width=20
                            xmlns=http://www.w3.org/2000/svg>
                            <g>
                                <g>
                                    <g>
                                        <path
                                            d="m334.419 441.105h-288.984c-11.822 0-21.406-9.597-21.406-21.436v-365.592c0-11.839 9.584-21.436 21.406-21.436h288.984c11.822 0 21.406 9.597 21.406 21.436v365.593c0 11.838-9.584 21.435-21.406 21.435z"
                                            fill=#fff></path>
                                        <path
                                            d="m66.771 111.124v-78h-21.337c-11.822 0-21.406 9.116-21.406 20.954v365.592c0 11.838 9.584 21.618 21.406 21.618h288.984c11.822 0 21.931-9.779 21.931-21.618v-18.985c-159.929 0-289.578-129.641-289.578-289.561z"
                                            fill=#e6e7e8></path>
                                        <g fill=#3e4d6c>
                                            <path
                                                d="m266.833 457.132h-221.398c-20.642 0-37.435-16.805-37.435-37.462v-365.593c0-20.657 16.793-37.463 37.435-37.463h36.259v32.055h-36.259c-2.966 0-5.378 2.426-5.378 5.408v365.593c0 2.982 2.413 5.408 5.378 5.408h221.398z">
                                            </path>
                                            <path
                                                d="m371.853 272.116h-32.057v-218.039c0-2.982-2.413-5.408-5.378-5.408h-33.625v-32.055h33.625c20.642 0 37.435 16.806 37.435 37.463z">
                                            </path>
                                        </g>
                                    </g>
                                    <path
                                        d="m252.2 64.307h-124.547c-9.941 0-18-8.059-18-18v-28.307c0-9.941 8.059-18 18-18h124.547c9.941 0 18 8.059 18 18v28.307c0 9.941-8.059 18-18 18z"
                                        fill=#0795fe></path>
                                    <g>
                                        <g>
                                            <path d="m195.308 245.118h115.718v32.055h-115.718z" fill=#3e4d6c></path>
                                        </g>
                                        <g>
                                            <path d="m195.308 148.658h115.718v32.055h-115.718z" fill=#3e4d6c></path>
                                        </g>
                                        <g>
                                            <path d="m195.308 341.578h54.811v32.055h-54.811z" fill=#3e4d6c></path>
                                        </g>
                                    </g>
                                </g>
                                <g>
                                    <path
                                        d="m290.05 367.087c-8.052 46.599 23.182 91.167 69.763 99.545 46.581 8.379 90.87-22.605 98.922-69.204s-23.182-91.167-69.763-99.545-90.87 22.605-98.922 69.204z"
                                        fill=#0795fe id=XMLID_6149_></path>
                                </g>
                                <path
                                    d="m353.927 415.026-29.001-29.524 22.847-22.51 17.534 17.805 35.181-36.031 22.933 22.392-46.603 47.825c-3 3.072-13.344 9.209-22.891.043z"
                                    fill=#fff></path>
                                <g fill=#3e4d6c>
                                    <path
                                        d="m93.641 389.156-31.127-33.901 23.624-21.647 19.318 21.073 45.919-50.036 23.617 21.675-57.731 62.839c-3.035 3.307-14.176 8.855-23.62-.003z">
                                    </path>
                                    <path
                                        d="m93.641 292.992-31.127-33.901 23.624-21.647 19.318 21.073 45.919-50.036 23.617 21.675-57.731 62.839c-3.035 3.306-14.176 8.854-23.62-.003z">
                                    </path>
                                    <path
                                        d="m93.641 195.759-31.127-33.901 23.624-21.647 19.318 21.073 45.919-50.036 23.617 21.675-57.731 62.839c-3.035 3.307-14.176 8.854-23.62-.003z">
                                    </path>
                                </g>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['phlebo_reassign_diagnostic'], $permissionPage) &&
                        $row['type_of_collection'] == 'HC' &&
                        $row['phlebo_assign_status'] == 1): ?>
                    <a href="<?php echo e(route('diagnostic.phleboAssign', $row['id'])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" title="Phlebo Reassign">
                        <svg id="fi_17938363" height="20" width="20" enable-background="new 0 0 100 100"
                            viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <g fill="#f46700">
                                <path
                                    d="m50 10.4166689c-11.0833282 0-20.5833378 4.7500029-27.7083321 11.874999l11.0833282 11.0833282c3.9583321-3.958332 10.2916718-7.1249942 16.6250039-7.1249942 13.4583359 0 23.75 10.2916698 23.75 23.7499981h-7.9166641l15.8333282 15.8333359 15.8333359-15.8333359h-7.9166641c0-22.1666641-17.4166718-39.5833311-39.5833359-39.5833311z">
                                </path>
                                <path
                                    d="m50 73.75c-13.4583359 0-23.75-10.2916603-23.75-23.75h7.9166679l-15.8333339-15.8333321-15.8333333 15.8333321h7.916666c0 22.1666565 17.4166679 39.5833282 39.5833321 39.5833282 11.0833282 0 20.5833359-4.7499924 27.7083359-11.8749924l-11.0833347-11.0833358c-3.9583282 4.7500076-10.2916679 7.125-16.625 7.125z">
                                </path>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['phlebo_assign_diagnostic'], $permissionPage) &&
                        $row['type_of_collection'] == 'HC' &&
                        $row['phlebo_assign_status'] == 0): ?>
                    <a href="<?php echo e(route('diagnostic.phleboAssign', $row['id'])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" title="Phlebo Assign">
                        <svg height="20" viewBox="0 -3 454.29886 454" width="20"
                            xmlns="http://www.w3.org/2000/svg" id="fi_1251314">
                            <path
                                d="m267 400.949219-267 .601562c-.199219-95.703125 71.101562-166 159.199219-166.101562 40.800781-.101563 78.101562 14.898437 106.300781 40.601562-28.820312 36.84375-28.195312 88.757813 1.5 124.898438zm0 0"
                                fill="#e48e66"></path>
                            <path
                                d="m159.300781.148438c52.851563 0 95.699219 42.847656 95.699219 95.699218 0 52.855469-42.847656 95.703125-95.699219 95.703125-52.855469 0-95.699219-42.847656-95.699219-95.703125 0-52.851562 42.84375-95.699218 95.699219-95.699218zm0 0"
                                fill="#f8ec7d"></path>
                            <path
                                d="m159.300781 235.347656h-.199219c-88 .203125-159.300781 70.5-159.101562 166.101563l159.300781-.300781zm0 0"
                                fill="#d18162"></path>
                            <path
                                d="m159.300781.148438c-52.855469 0-95.699219 42.847656-95.699219 95.699218 0 52.855469 42.84375 95.703125 95.699219 95.703125zm0 0"
                                fill="#e2d574"></path>
                            <path
                                d="m344.300781 227.449219c-33.839843-.078125-65.808593 15.5-86.601562 42.199219l-.097657.101562c-15.15625 19.304688-23.367187 43.15625-23.300781 67.699219.027344 25.453125 8.859375 50.117187 25 69.800781 1.601563 1.898438 3.300781 3.800781 5 5.699219 34.441407 36.496093 89.21875 45.054687 133.152344 20.804687 43.933594-24.246094 65.886719-75.152344 53.367187-123.746094-12.519531-48.597656-56.339843-82.558593-106.519531-82.558593zm0 0"
                                fill="#63316d"></path>
                            <path
                                d="m328.402344 370.148438c-2.921875-.007813-5.695313-1.285157-7.601563-3.5l-24-28.097657c-3.589843-4.199219-3.097656-10.511719 1.101563-14.101562 4.195312-3.589844 10.507812-3.097657 14.097656 1.101562l17.699219 20.699219 48.300781-39.300781c4.28125-3.507813 10.59375-2.878907 14.101562 1.398437 3.507813 4.28125 2.878907 10.59375-1.402343 14.101563l-55.898438 45.5c-1.835937 1.414062-4.082031 2.1875-6.398437 2.199219zm0 0"
                                fill="#f8ec7d"></path>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['edit_diagnostic'], $permissionPage)): ?>
                    <a class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" href="<?php echo e(route('diagnostic.addForm', ['id' => $row['id']])); ?>" title="Edit">
                        <svg height=20 id=fi_1828270 viewBox="0 0 512 511" width=20 xmlns=http://www.w3.org/2000/svg>
                            <path
                                d="m362.667969 512.484375h-298.667969c-35.285156 0-64-28.714844-64-64v-298.667969c0-35.285156 28.714844-64 64-64h170.667969c11.796875 0 21.332031 9.558594 21.332031 21.335938 0 11.773437-9.535156 21.332031-21.332031 21.332031h-170.667969c-11.777344 0-21.332031 9.578125-21.332031 21.332031v298.667969c0 11.753906 9.554687 21.332031 21.332031 21.332031h298.667969c11.773437 0 21.332031-9.578125 21.332031-21.332031v-170.667969c0-11.773437 9.535156-21.332031 21.332031-21.332031s21.335938 9.558594 21.335938 21.332031v170.667969c0 35.285156-28.714844 64-64 64zm0 0"
                                fill=#607d8b></path>
                            <g fill=#42a5f5>
                                <path
                                    d="m368.8125 68.261719-168.792969 168.789062c-1.492187 1.492188-2.496093 3.390625-2.921875 5.4375l-15.082031 75.4375c-.703125 3.496094.40625 7.101563 2.921875 9.640625 2.027344 2.027344 4.757812 3.113282 7.554688 3.113282.679687 0 1.386718-.0625 2.089843-.210938l75.414063-15.082031c2.089844-.429688 3.988281-1.429688 5.460937-2.925781l168.789063-168.789063zm0 0">
                                </path>
                                <path
                                    d="m496.382812 16.101562c-20.796874-20.800781-54.632812-20.800781-75.414062 0l-29.523438 29.523438 75.414063 75.414062 29.523437-29.527343c10.070313-10.046875 15.617188-23.445313 15.617188-37.695313s-5.546875-27.648437-15.617188-37.714844zm0 0">
                                </path>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['view_report_diagnostic'], $permissionPage) && $row['unique_id'] != null && $reported_status): ?>
                    <a title="View Report"
                        class="btn btn-light mt-0 icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" target="_blank"
                        href="<?php echo e($itdose_report_url.'?IsPrev=0&PHead=1&LedgerTransactionNo_Interface='.$row['unique_id']); ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            version="1.1" id="fi_1690427" x="0px" y="0px" viewBox="0 0 512 512"
                            style="enable-background:new 0 0 512 512;" xml:space="preserve" width="20"
                            height="20">
                            <g>
                                <path
                                    d="M414,28.055h-56.325C342.812,10.889,320.908,0,296.5,0h-81c-24.408,0-46.312,10.889-61.175,28.055H98   c-24.813,0-45,20.227-45,45.088v393.769C53,491.773,73.187,512,98,512h316c24.813,0,45-20.227,45-45.088V73.143   C459,48.281,438.813,28.055,414,28.055z M215.5,30.059h81c23.177,0,42.425,15.515,48.75,36.07H166.75   C173.064,45.61,192.287,30.059,215.5,30.059z M429,466.912c0,8.287-6.729,15.029-15,15.029H98c-8.271,0-15-6.742-15-15.029V73.143   c0-8.287,6.729-15.029,15-15.029h39.846c-2.165,7.31-3.346,15.04-3.346,23.045c0,8.3,6.716,15.029,15,15.029h213   c8.284,0,15-6.729,15-15.029c0-8.005-1.181-15.735-3.346-23.045H414c8.271,0,15,6.742,15,15.029V466.912z">
                                </path>
                                <path
                                    d="M182.06,298.644L160,320.746l-5.394-5.404c-5.857-5.87-15.355-5.87-21.213,0c-5.858,5.869-5.858,15.385,0,21.254l16,16.031   c5.857,5.869,15.355,5.87,21.213,0l32.666-32.73c5.858-5.869,5.858-15.385,0-21.254   C197.415,292.774,187.917,292.774,182.06,298.644z">
                                </path>
                                <path
                                    d="M367,310.607H246c-8.284,0-15,6.729-15,15.029c0,8.3,6.716,15.029,15,15.029h121c8.284,0,15-6.729,15-15.029   C382,317.336,375.284,310.607,367,310.607z">
                                </path>
                                <path
                                    d="M182.06,384.812L160,406.914l-5.394-5.404c-5.857-5.87-15.355-5.87-21.213,0c-5.858,5.869-5.858,15.385,0,21.254l16,16.031   c5.857,5.869,15.355,5.87,21.213,0l32.666-32.73c5.858-5.869,5.858-15.385,0-21.254   C197.415,378.942,187.917,378.942,182.06,384.812z">
                                </path>
                                <path
                                    d="M367,396.775H246c-8.284,0-15,6.729-15,15.029c0,8.3,6.716,15.029,15,15.029h121c8.284,0,15-6.729,15-15.029   C382,403.504,375.284,396.775,367,396.775z">
                                </path>
                                <path
                                    d="M284.606,241.077L331,194.593v1.791c0,8.3,6.716,15.029,15,15.029s15-6.729,15-15.029c0-42.513,0.07-38.555-0.172-40.234   c-1.061-7.353-7.353-12.872-14.833-12.87H308c-8.284,0-15,6.729-15,15.029c0,8.3,6.716,15.029,15,15.029h1.787L274,209.195   l-45.394-45.482c-5.857-5.87-15.355-5.87-21.213,0l-54,54.106c-5.858,5.869-5.858,15.385,0,21.254c5.857,5.87,15.355,5.87,21.213,0   L218,195.595l45.394,45.482C269.25,246.946,278.749,246.947,284.606,241.077z">
                                </path>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['print_report_diagnostic'], $permissionPage) && $row['unique_id'] != null && $reported_status): ?>
                    <a target="_blank" title="Print Report"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button"
                        href="<?php echo e($itdose_report_url.'?IsPrev=0&PHead=0&LedgerTransactionNo_Interface='.$row['unique_id']); ?>">
                        <svg height=16 id=fi_446991 style="enable-background:new 0 0 512 512"version=1.1
                            viewBox="0 0 512 512"width=16 x=0px xml:space=preserve xmlns=http://www.w3.org/2000/svg
                            xmlns:xlink=http://www.w3.org/1999/xlink y=0px>
                            <g>
                                <g>
                                    <path
                                        d="M329.956,399.834H182.044c-9.425,0-17.067,7.641-17.067,17.067s7.641,17.067,17.067,17.067h147.911          c9.425,0,17.067-7.641,17.067-17.067S339.381,399.834,329.956,399.834z">
                                    </path>
                                </g>
                            </g>
                            <g>
                                <g>
                                    <path
                                        d="M329.956,346.006H182.044c-9.425,0-17.067,7.641-17.067,17.067s7.641,17.067,17.067,17.067h147.911 c9.425,0,17.067-7.641,17.067-17.067S339.381,346.006,329.956,346.006z">
                                    </path>
                                </g>
                            </g>
                            <g>
                                <g>
                                    <path
                                        d="M472.178,133.907h-54.303V35.132c0-9.425-7.641-17.067-17.067-17.067H111.192c-9.425,0-17.067,7.641-17.067,17.067v98.775       H39.822C17.864,133.907,0,151.772,0,173.73v171.702c0,21.958,17.864,39.822,39.822,39.822h54.306v91.614            c0,9.425,7.641,17.067,17.067,17.067h289.61c9.425,0,17.067-7.641,17.067-17.067v-91.614h54.306            c21.958,0,39.822-17.864,39.822-39.822V173.73C512,151.773,494.136,133.907,472.178,133.907z M128.258,52.199h255.483v81.708            H128.258V52.199z M383.738,459.801H128.262c0-3.335,0-135.503,0-139.628h255.477C383.738,324.402,383.738,456.594,383.738,459.801           z M400.808,234.122h-43.443c-9.425,0-17.067-7.641-17.067-17.067s7.641-17.067,17.067-17.067h43.443            c9.425,0,17.067,7.641,17.067,17.067S410.234,234.122,400.808,234.122z">
                                    </path>
                                </g>
                            </g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['upload_prescription_diagnostic', 'show_prescription_diagnostic'], $permissionPage)): ?>
                    <div class="dropdown position-static">
                        <button
                            class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                            type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                            <svg xmlns="http://www.w3.org/2000/svg" height="16" version="1.1"
                                viewBox="-23 0 512 512.00072" width="16" id="fi_1092000">
                                <g id="surface1">
                                    <path
                                        d="M 348.945312 221.640625 L 348.945312 124.746094 C 348.945312 121.972656 347.664062 119.410156 345.851562 117.382812 L 237.21875 3.308594 C 235.191406 1.175781 232.308594 0 229.429688 0 L 57.195312 0 C 25.398438 0 0 25.929688 0 57.730469 L 0 383.414062 C 0 415.214844 25.398438 440.71875 57.195312 440.71875 L 193.148438 440.71875 C 218.863281 483.402344 265.605469 512 318.851562 512 C 399.738281 512 465.792969 446.265625 465.792969 365.273438 C 465.902344 294.523438 415.105469 235.40625 348.945312 221.640625 Z M 240.101562 37.457031 L 312.984375 114.179688 L 265.710938 114.179688 C 251.625 114.179688 240.101562 102.550781 240.101562 88.464844 Z M 57.195312 419.375 C 37.242188 419.375 21.34375 403.367188 21.34375 383.414062 L 21.34375 57.730469 C 21.34375 37.667969 37.242188 21.34375 57.195312 21.34375 L 218.757812 21.34375 L 218.757812 88.464844 C 218.757812 114.394531 239.78125 135.523438 265.710938 135.523438 L 327.601562 135.523438 L 327.601562 218.863281 C 324.402344 218.757812 321.839844 218.4375 319.066406 218.4375 C 281.824219 218.4375 247.570312 232.738281 221.746094 255.148438 L 86.222656 255.148438 C 80.351562 255.148438 75.550781 259.949219 75.550781 265.816406 C 75.550781 271.6875 80.351562 276.488281 86.222656 276.488281 L 201.898438 276.488281 C 194.320312 287.160156 188.023438 297.832031 183.117188 309.570312 L 86.222656 309.570312 C 80.351562 309.570312 75.550781 314.371094 75.550781 320.242188 C 75.550781 326.109375 80.351562 330.914062 86.222656 330.914062 L 176.179688 330.914062 C 173.511719 341.585938 172.125 353.429688 172.125 365.273438 C 172.125 384.480469 175.859375 403.476562 182.582031 419.484375 L 57.195312 419.484375 Z M 318.960938 490.765625 C 249.8125 490.765625 193.574219 434.527344 193.574219 365.378906 C 193.574219 296.230469 249.703125 239.992188 318.960938 239.992188 C 388.214844 239.992188 444.34375 296.230469 444.34375 365.378906 C 444.34375 434.527344 388.109375 490.765625 318.960938 490.765625 Z M 318.960938 490.765625 "
                                        style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                        fill="currentColor">
                                    </path>
                                    <path
                                        d="M 86.222656 223.027344 L 194.320312 223.027344 C 200.191406 223.027344 204.992188 218.222656 204.992188 212.355469 C 204.992188 206.484375 200.191406 201.683594 194.320312 201.683594 L 86.222656 201.683594 C 80.351562 201.683594 75.550781 206.484375 75.550781 212.355469 C 75.550781 218.222656 80.351562 223.027344 86.222656 223.027344 Z M 86.222656 223.027344 "
                                        style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                        fill="currentColor">
                                    </path>
                                    <path
                                        d="M 373.59375 363.136719 L 329.738281 410.410156 L 329.738281 293.882812 C 329.738281 288.011719 324.933594 283.210938 319.066406 283.210938 C 313.195312 283.210938 308.394531 288.011719 308.394531 293.882812 L 308.394531 410.410156 L 264.214844 363.136719 C 260.160156 358.871094 253.332031 358.550781 249.0625 362.605469 C 244.792969 366.660156 244.472656 373.382812 248.53125 377.652344 L 310.957031 444.773438 C 312.984375 446.90625 315.757812 448.1875 318.746094 448.1875 C 321.734375 448.1875 324.507812 446.90625 326.535156 444.773438 L 389.070312 377.652344 C 393.125 373.382812 392.910156 366.554688 388.640625 362.605469 C 384.265625 358.550781 377.652344 358.871094 373.59375 363.136719 Z M 373.59375 363.136719 "
                                        style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                        fill="currentColor">
                                    </path>
                                </g>
                            </svg>
                        </button>
                        <ul class="dropdown-menu bg-light p-0" aria-labelledby="dropdownMenuButton1">
                            <?php if(array_intersect(['show_prescription_diagnostic'], $permissionPage) && $row['prescription_upload']): ?>
                                <li>
                                    <a class="dropdown-item h6 border-bottom" target="_blank" href="<?php echo e(Storage::disk('gcs')->url($row['prescription_upload'])); ?>">
                                        <svg version="1.1" id="fi_159604" height="16" width="16"
                                            xmlns="http://www.w3.org/2000/svg"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                            viewBox="0 0 488.85 488.85"
                                            style="enable-background:new 0 0 488.85 488.85;" xml:space="preserve">
                                            <g>
                                                <path d="M244.425,98.725c-93.4,0-178.1,51.1-240.6,134.1c-5.1,6.8-5.1,16.3,0,23.1c62.5,83.1,147.2,134.2,240.6,134.2
                                  s178.1-51.1,240.6-134.1c5.1-6.8,5.1-16.3,0-23.1C422.525,149.825,337.825,98.725,244.425,98.725z M251.125,347.025
                                  c-62,3.9-113.2-47.2-109.3-109.3c3.2-51.2,44.7-92.7,95.9-95.9c62-3.9,113.2,47.2,109.3,109.3
                                  C343.725,302.225,302.225,343.725,251.125,347.025z M248.025,299.625c-33.4,2.1-61-25.4-58.8-58.8c1.7-27.6,24.1-49.9,51.7-51.7
                                  c33.4-2.1,61,25.4,58.8,58.8C297.925,275.625,275.525,297.925,248.025,299.625z"></path>
                                            </g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                            <g></g>
                                        </svg><small class="ms-2">View</small>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if(array_intersect(['upload_prescription_diagnostic'], $permissionPage)): ?>
                                <li>
                                    <a class="dropdown-item h6" role="button" title="Info" data-bs-toggle="modal"
                                        data-bs-target="#uploadPrescriptionModal"
                                        onclick="uploadPrescription('<?php echo e($row['id']); ?>')">
                                        <svg xmlns="http://www.w3.org/2000/svg" id="fi_3097412" data-name="Layer 2"
                                            width="16" height="16" viewBox="0 0 24 24">
                                            <path
                                                d="M22,13a1,1,0,0,0-1,1v4.213A2.79,2.79,0,0,1,18.213,21H5.787A2.79,2.79,0,0,1,3,18.213V14a1,1,0,0,0-2,0v4.213A4.792,4.792,0,0,0,5.787,23H18.213A4.792,4.792,0,0,0,23,18.213V14A1,1,0,0,0,22,13Z">
                                            </path>
                                            <path
                                                d="M6.707,8.707,11,4.414V17a1,1,0,0,0,2,0V4.414l4.293,4.293a1,1,0,0,0,1.414-1.414l-6-6a1,1,0,0,0-1.414,0l-6,6A1,1,0,0,0,6.707,8.707Z">
                                            </path>
                                        </svg>
                                        <small class="ms-2">Upload</small></a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Diagnostic\resources/views/diagnostic/api/list.blade.php ENDPATH**/ ?>