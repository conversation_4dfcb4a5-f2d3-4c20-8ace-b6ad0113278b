<?php

namespace Modules\Patient\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MemberRequest extends FormRequest
{
    protected $id;
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $this->id = $this->route('id') ? $this->route('id') : '';
        $rules = [
            'name'  => 'required|string|between:2,100',
            'sex'  => 'required',
            'birthdate' => ['required', 'date', 'before_or_equal:' . now()->subDay()->format('Y-m-d')],
            'language_id'  => 'required',
            'relationship_id'  => 'required'
        ];
        return $rules;
    }
    public function messages(): array
    {
        return [
            'sex.required' => 'The gender field is required.',
            'language_id.required' => 'The language field is required.',
            'relationship_id.required' => 'The relationship field is required.'
        ];
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
