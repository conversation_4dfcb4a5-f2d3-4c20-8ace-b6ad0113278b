<?php $__env->startSection('title'); ?>
<?php echo e(config('appointment.title', 'appointment')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="col-sm-12">
    <div class="card">
        <div class="card-header d-flex justify-content-between">
            <div class="header-title">
                <h4 class="card-title">Appointments</h4>
            </div>

            <div class="header-action">

            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <label class="mb-1 d-flex gap-2 align-items-center">
                        <span>Show</span>
                        <select id="perPageCount" class="form-select form-select-sm px-1">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>entries</span>
                    </label>
                </div>
                <div class="col-md-10 ps-md-5">
                    <div class="row justify-content-end">
                        <?php if($stat == 'all'): ?>
                        <div class="col-md col-6 px-1 mb-1 mb-md-0">
                            <select name="doctor_id" class="select2-multpl-custom1 form-select search-change"
                                data-style="py-0">
                                <option value="">Filter By Doctor</option>
                                <?php $__currentLoopData = $data['doctor_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($row['id']); ?>"><?php echo e($row['username']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md col-6 px-1 mb-1 mb-md-0">
                            <select name="clinic_id" class="select2-multpl-custom1 form-select search-change"
                                data-style="py-0">
                                <option value="">Filter By Clinic</option>
                                <?php $__currentLoopData = $data['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($row['id']); ?>"><?php echo e($row['clinic_name']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md col-6 px-1 mb-1 mb-md-0">
                            <input type="text" name="date" id="date_range"
                                placeholder="Please select a date range"
                                class="form-control form-control-sm flatpickr-input active search-date-range"
                                readonly="readonly">
                        </div>
                        <?php endif; ?>
                        <div class="col-md col-6 px-1 mb-1 mb-md-0" style="max-width: 230px;">
                            <input type="search" class="form-control form-control-sm search" placeholder="Search" 
                                data-index="0,1">

                        </div>

                        <?php if($stat == 'all'): ?>
                        <div class="col-md  px-1 mb-1 mb-md-0" style="max-width: 90px;">
                            <a href="<?php echo e(route('appointment.index',[($stat == 'all' ? $stat : 'today')])); ?>"
                                class="btn btn-sm btn-primary d-flex gap-1 align-items-center justify-content-center" >
                                Reset
                            </a>
                        </div>
                        <?php endif; ?>




                    </div>
                </div>
            </div>

            <div class="Table-custom-padding1 table-responsive">
                <table id="data-list" class="table table-sm datatable_desc placeholder-glow" data-toggle="data-table">
                    <thead>
                        <tr>
                            <th>
                                ID
                                <span class="asc-dsc float-end">
                                    <button type="button" data-index="0" data-sort="asc" class="sort"><svg
                                            xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                            <path
                                                d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                        </svg></button>
                                    <button type="button" data-index="0" data-sort="desc" class="sort"><svg
                                            xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                            <path
                                                d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                        </svg></button>
                                </span>
                            </th>
                            <th>
                                Patient
                                
                            </th>
                            <th>
                                Patient Phone
                                <span class="asc-dsc float-end">
                                    <button type="button" data-index="1" data-sort="asc" class="sort"><svg
                                            xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                            <path
                                                d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                        </svg></button>
                                    <button type="button" data-index="1" data-sort="desc" class="sort"><svg
                                            xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                            <path
                                                d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                        </svg></button>
                                </span>
                            </th>
                            <th>
                                Membership Details
                            </th>
                            <th>
                                Doctor
                            </th>
                            <th>
                                Date - Time
                            </th>
                            <th>
                                Clinic
                            </th>
                            <th>
                                Remarks
                            </th>
                            <th>
                                Status
                            </th>
                            <th>
                                Bill Status
                            </th>
                            <th>
                                Action
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php echo $__env->make('admin.custom.loading', ['td' => 11, 'action' => 3], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </tbody>
                    <tfoot>
                        <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
<div class="modal fade" id="exampleInfoModal" tabindex="-1" aria-labelledby="exampleModalCenteredScrollableTitle"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content" id="info-div-modal">
            <div class="modal-header border-0">
                <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Appointment Info</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
<script>
    $("#date_range").flatpickr({
        mode: "range",
        //   maxDate: "today"
    });
    $(document).ready(function() {
        let ht_id = '#data-list';
        setId(ht_id); // set show table id
        let url = "<?php echo e(config('appointment.url') . 'list'); ?>";
        setListUrl(url); // api url for show table
        let method = 'POST';
        setMethod(method);
        let sortCollumns = [
            "id",
            "patient_phone"
        ];
        setSortCollumns(sortCollumns);
        let perPage = 10; // set items per page
        itemsPerPage(perPage);
        let page = 1; // set current page
        currentPage(page);
        let search = "2"; // set search
        let filter = {
            "filter": {

            },
            "filtermulti": {

            },
            "pagination": {
                "limit": 3,
                "offset": 0
            },
            "sort": {
                "id": "desc"
            }
        };
        <?php if($stat == 'today'): ?>
        filter["stat"] = "today";
        <?php endif; ?>
        setFilter(filter); // set filter [where, pagination, sort]
        paginationFilter(); // by default first pagination
        getList(); // get list data with pagination and filter
    });

    function updateAppointmentStatus(url, method, parent) {
        // console.log(url,method);
        if (!jwtToken) {
            redirectToSwal(loginUrl);
            return false;
        }
        // let field = $(parent).attr('name');
        let status = $(parent).val();
        // console.log(field, status);
        // return false;
        $.ajax({
            type: method,
            url: url,
            crossDomain: true,
            dataType: 'json',
            cache: false, // Corrected from 'catch' to 'cache'
            contentType: "application/json",
            headers: {
                'Accept': 'application/json',
                'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
            },
            data: JSON.stringify({
                status: status
            }), // Convert data object to JSON string
            processData: false, // Prevent jQuery from automatically transforming the data into a query string
            success: function(data) {
                // console.log(data);
                if (data.success == true) {
                    successAlertSwal(data);
                } else {
                    errorAlertSwal(data);
                }
                getList();
            },
            error: function(response) {
                console.error(response); // Log the error response for debugging
                // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
            }
        });
    }

    function previousPrescriptionModal(data, pp, prescription_doctor_id) {
        if (pp && !prescription_doctor_id) {
            console.log($(data).attr('href'));
            swal({
                title: "",
                text: 'Do You Want To Continue with previous prescription?',
                type: "warning",
                html: true,
                showCancelButton: true,
                confirmButtonColor: '#e04848',
                cancelButtonText: "New Prescription",
                confirmButtonText: 'Yes, Continue',
            }, function(isConfirm) {
                if (isConfirm) {
                    window.location.href = $(data).attr('href') + '&pp=' + pp;
                } else {
                    window.location.href = $(data).attr('href');
                }
            });
            return false;
        } else {
            return true;
        }
    }
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/index.blade.php ENDPATH**/ ?>