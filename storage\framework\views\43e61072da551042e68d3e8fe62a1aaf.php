<?php $__env->startSection('title'); ?>
    <?php echo e(config('diagnostic.title', 'Diagnostic')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
    <style>
        .tooltip1 {
            display: block;
            position: relative;
        }

        .tooltip1 .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #626262;
            color: #fff;
            border-radius: 6px;
            padding: 0px;
            position: absolute;
            z-index: 100;
            top: 10%;
            right: 27px;
            font-size: 11px;
            padding-right: 0;
            text-align: start;
        }

        .tooltip1 .tooltiptext::after {
            content: "";
            position: absolute;
            top: 15px;
            right: -10px;
            margin-top: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent transparent #343434;
        }

        .tooltip1:hover .tooltiptext {
            visibility: visible;
        }

        .icon-wrap {
            /* width: 83px;
                                                text-align: center; */
            width: 100%;
            text-align: center;
            display: flex;
            flex-wrap: nowrap;
            flex-direction: row !important;
            gap: 5px;
            align-items: center !important;
        }

        @media only screen and (max-width:767px) {

            .icon-wrap {
                width: auto;
            }

        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title">Diagnostics</h4>
                </div>

                <div class="header-action">

                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <label class="mb-1 d-flex gap-2 align-items-center">
                            <span>Show</span>
                            <select id="perPageCount" class="form-select form-select-sm px-1">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>entries</span>
                        </label>
                    </div>

                    <div class="col-md-10 ps-md-5">
                        <div class="row justify-content-end">
                            <div class="col-md col-6 px-1 mb-1 mb-md-0">
                                <select name="type_of_collection" id="type_of_collection"
                                    class="select2-multpl-custom1 form-select search-change" data-style="py-0">
                                    <option value="">Filter By Visit Type</option>
                                    <?php $__currentLoopData = $data['visit_type']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>">
                                            <?php echo e($row); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md col-6 px-1 mb-1 mb-md-0">
                                <select name="date_of_collection" id="date_type"
                                    class="select2-multpl-custom1 form-select search-date-range" data-style="py-0">
                                    <option value="<?php echo e(date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d') ? 'selected' : ''); ?>>Today</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-1 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-1 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Yesterday
                                    </option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-7 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-7 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        7
                                        Days</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-15 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-15 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        15
                                        Days</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-30 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-30 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        30
                                        Days</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-60 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-60 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        60
                                        Days</option>
                                    <option value="<?php echo e(date('Y-m-d', strtotime('-90 days')) . ' to ' . date('Y-m-d')); ?>"
                                        <?php echo e(request('d_type') == date('Y-m-d', strtotime('-90 days')) . ' to ' . date('Y-m-d') ? 'selected' : ''); ?>>
                                        Past
                                        90
                                        Days</option>
                                </select>
                            </div>
                            <div class="col-md col-6 px-1 mb-1 mb-md-0">
                                <input type="text" name="date_of_collection" id="date_range"
                                    placeholder="Please select a date range"
                                    class="form-control form-control-sm flatpickr-input search-date-range active"
                                    readonly="readonly" value="<?php echo e(request('d_range')); ?>">
                            </div>
                            <div class="col-md col-6 px-1 mb-1 mb-md-0">
                                <select name="clinic_id" id="clinic_id"
                                    class="select2-multpl-custom1 form-select search-change" data-style="py-0">
                                    <option value="">Filter By Clinic</option>
                                    <?php $__currentLoopData = $data['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($row['id']); ?>">
                                            <?php echo e($row['clinic_name']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md col-6 px-1 mb-1 mb-md-0">
                                <input type="search" class="form-control form-control-sm search"
                                    placeholder="Search by Name or Phone No" data-index="1,2">

                            </div>
                            <div class="col-md  px-1 mb-1 mb-md-0" style="max-width: 90px;">
                                <a href="<?php echo e(route('diagnostic.index')); ?>" class="btn btn-sm btn-primary d-flex gap-1 align-items-center justify-content-center">
                                    Reset
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-wrap flex-md-nowrap  mt-4 mb-2 justify-content-start gap-2 w-100">
                    <div class="icon-list d-flex flex-wrap flex-md-nowrap p-0 gap-1 w-100">
                        <div class="icon-wrap flex-column align-items-center border border-1 p-2 ">
                            <svg clip-rule="evenodd" fill-rule="evenodd" height="20" image-rendering="optimizeQuality"
                                shape-rendering="geometricPrecision" text-rendering="geometricPrecision"
                                viewBox="0 0 14872 14872" width="20" xmlns="http://www.w3.org/2000/svg"
                                id="fi_11524741">
                                <g id="Layer_x0020_1">
                                    <g id="_959695072">
                                        <path
                                            d="m0 0h10896v1147c-194 9-386 32-572 66v-640h-9751v13726h9751v-754h572v1327h-10896zm10896 9746v1673h-572v-1739c186 34 378 57 572 66z"
                                            fill="#333"></path>
                                        <path
                                            d="m12361 11959h-528v1046h528c288 0 523-235 523-523 0-288-235-523-523-523zm-7364 0h6459v1046h-6459zm-338 0-1055 399h-426c-69 0-124 56-124 124 0 68 55 124 124 124h426l1055 399v-523z"
                                            fill="#04599c"></path>
                                    </g>
                                    <path
                                        d="m11110 1685c2078 0 3762 1684 3762 3761 0 2078-1684 3762-3762 3762-406 0-797-65-1164-184-396 360-1050 681-1870 569 0 0 565-491 838-1093-948-683-1565-1796-1565-3054 0-2077 1684-3761 3761-3761zm0 2433c341 0 618-277 618-618s-277-617-618-617c-340 0-617 276-617 617s277 618 617 618zm-348 3805h697c128 0 232-105 232-233v-2741c0-128-104-232-232-232h-697c-128 0-232 104-232 232v2741c0 128 104 233 232 233z"
                                        fill="#04599c"></path>
                                    <g fill="#333">
                                        <path d="m4096 1866h3447v697h-3447z"></path>
                                        <path d="m4096 3319h2576v697h-2576z"></path>
                                        <path d="m4096 6840h2816v697h-2816z"></path>
                                        <path d="m4096 8293h2576v697h-2576z"></path>
                                    </g>
                                    <path d="m1392 2794 677 462 1071-1540 476 331-1234 1776-164 236-238-162-915-625z"
                                        fill="#04599c" fill-rule="nonzero"></path>
                                    <path d="m1392 7651 677 463 1071-1541 476 332-1234 1776-164 236-238-162-915-625z"
                                        fill="#04599c" fill-rule="nonzero"></path>
                                </g>
                                <g id="boxes">
                                    <path d="m0 0h14872v14872h-14872z" fill="none"></path>
                                </g>
                            </svg>
                            <h6 class="h8 ">Details</h6>
                        </div>
                        <div class="icon-wrap flex-column align-items-center border border-1 p-2 ">
                            <svg id="fi_4773546" enable-background="new 0 0 512 512" height="20" viewBox="0 0 512 512"
                                width="20" xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path
                                        d="m512 75v-30c0-24.813-20.187-45-45-45h-422c-24.813 0-45 20.187-45 45v30l13 34-13 26v180c0 24.813 20.187 45 45 45h452c8.284 0 15-6.716 15-15v-210l-19-24z"
                                        fill="#0a789b"></path>
                                    <path
                                        d="m467 0h-211v360h241c8.284 0 15-6.716 15-15v-210l-19-24 19-36v-30c0-24.813-20.187-45-45-45z"
                                        fill="#08475e"></path>
                                    <path d="m0 75h512v60h-512z" fill="#ffe278"></path>
                                    <path d="m256 75h256v60h-256z" fill="#ffc178"></path>
                                    <g>
                                        <g>
                                            <g>
                                                <path
                                                    d="m115 230h-40c-8.284 0-15-6.716-15-15s6.716-15 15-15h40c8.284 0 15 6.716 15 15s-6.716 15-15 15z"
                                                    fill="#fff5f5"></path>
                                            </g>
                                            <g>
                                                <path
                                                    d="m241 290h-166c-8.284 0-15-6.716-15-15s6.716-15 15-15h166c8.284 0 15 6.716 15 15s-6.716 15-15 15z"
                                                    fill="#fff5f5"></path>
                                            </g>
                                            <g>
                                                <path
                                                    d="m215 230h-40c-8.284 0-15-6.716-15-15s6.716-15 15-15h40c8.284 0 15 6.716 15 15s-6.716 15-15 15z"
                                                    fill="#fff5f5"></path>
                                            </g>
                                        </g>
                                    </g>
                                    <g>
                                        <g>
                                            <g>
                                                <g>
                                                    <g>
                                                        <g>
                                                            <g>
                                                                <g>
                                                                    <g>
                                                                        <g>
                                                                            <path
                                                                                d="m407 512c-2.076 0-4.153-.431-6.092-1.293-60.085-26.704-98.908-86.444-98.908-152.195v-61.512c0-5.928 3.491-11.3 8.908-13.707 63.762-28.339 128.422-28.339 192.184 0 5.417 2.407 8.908 7.779 8.908 13.707v61.512c0 65.751-38.823 125.491-98.908 152.195-1.939.862-4.016 1.293-6.092 1.293z"
                                                                                fill="#a2e786"></path>
                                                                            <g>
                                                                                <path
                                                                                    d="m503.092 283.293c-31.881-14.169-63.986-21.254-96.092-21.254v249.961c2.076 0 4.153-.431 6.092-1.293 60.085-26.704 98.908-86.444 98.908-152.195v-61.512c0-5.928-3.491-11.3-8.908-13.707z"
                                                                                    fill="#00cb75"></path>
                                                                            </g>
                                                                            <path
                                                                                d="m332 306.951v51.561c0 51.65 29.224 98.778 75 121.892 45.776-23.113 75-70.241 75-121.892v-51.561c-50.373-20.085-99.627-20.085-150 0z"
                                                                                fill="#fff5f5"></path>
                                                                            <path
                                                                                d="m482 358.512v-51.561c-25.187-10.042-50.093-15.064-75-15.064v188.516c45.776-23.113 75-70.241 75-121.891z"
                                                                                fill="#e2dff4"></path>
                                                                            <g>
                                                                                <path
                                                                                    d="m385.787 418.82-21.213-21.214c-5.858-5.858-5.858-15.355 0-21.213 5.858-5.858 15.355-5.858 21.213 0l10.607 10.607 31.819-31.82c5.858-5.858 15.355-5.858 21.213 0 5.858 5.858 5.858 15.355 0 21.213l-42.426 42.427c-5.858 5.858-15.355 5.858-21.213 0z"
                                                                                    fill="#0a789b"></path>
                                                                            </g>
                                                                            <path
                                                                                d="m449.426 355.18c-5.858-5.858-15.355-5.858-21.213 0l-21.213 21.213v42.427l42.426-42.427c5.858-5.858 5.858-15.356 0-21.213z"
                                                                                fill="#08475e"></path>
                                                                        </g>
                                                                    </g>
                                                                </g>
                                                            </g>
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                            <h6 class="h8 ">Payment Settlement</h6>
                        </div>
                        <div class="icon-wrap flex-column align-items-center border border-1 p-2 ">
                            <svg id="fi_16963082" height="20" width="20" viewBox="0 0 64 64"
                                xmlns="http://www.w3.org/2000/svg" data-name="Filled Line">
                                <g>
                                    <path
                                        d="m53 51.9762v-46.233c0-.7723-.8382-1.2531-1.5048-.8632l-3.1204 1.8248c-.3118.1824-.6978.1824-1.0096 0l-4.1204-2.4096c-.3118-.1824-.6978-.1824-1.0096 0l-4.1199 2.4093c-.3121.1825-.6984.1824-1.0104-.0004l-4.1094-2.4079c-.3122-.1829-.6989-.1829-1.0111 0l-4.1089 2.4076c-.3122.1829-.6989.1829-1.0111 0l-4.1094-2.4079c-.3119-.1828-.6983-.1829-1.0104-.0004l-4.1194 2.409c-.3121.1825-.6984.1824-1.0104-.0004l-3.1094-1.8219c-.6666-.3906-1.5055.0901-1.5055.8628v38.255h-4v16h36.9762l8.0238-8.0238z"
                                        fill="#eceff1"></path>
                                    <circle cx="43" cy="43" fill="#388e3c" r="17"></circle>
                                    <path d="m4 44h8v12c0 2.2077-1.7923 4-4 4-2.2077 0-4-1.7923-4-4z" fill="#cfd8dc">
                                    </path>
                                </g>
                                <g fill="#030611">
                                    <path
                                        d="m12 19.4976c.4141 0 .75-.3359.75-.75v-1.5674c0-.4141-.3359-.75-.75-.75s-.75.3359-.75.75v1.5674c0 .4141.3359.75.75.75z">
                                    </path>
                                    <path
                                        d="m18.5 22.75h28c.4141 0 .75-.3359.75-.75s-.3359-.75-.75-.75h-28c-.4141 0-.75.3359-.75.75s.3359.75.75.75z">
                                    </path>
                                    <path
                                        d="m18.5 15.75h28c.4141 0 .75-.3359.75-.75s-.3359-.75-.75-.75h-28c-.4141 0-.75.3359-.75.75s.3359.75.75.75z">
                                    </path>
                                    <path
                                        d="m59.7432 37.0938c-.1377-.3911-.5684-.5947-.9561-.458-.3906.1382-.5957.5664-.458.957.6113 1.7334.9209 3.5527.9209 5.4072 0 8.9604-7.29 16.25-16.25 16.25s-16.25-7.2896-16.25-16.25 7.29-16.25 16.25-16.25c5.1699 0 10.0791 2.4961 13.1328 6.6772.2432.334.7109.4072 1.0479.1631.334-.2441.4072-.7134.1631-1.0479-1.0232-1.4014-2.2388-2.6275-3.5938-3.6567v-23.1455c0-.6348-.3301-1.2017-.8818-1.5171-.5488-.314-1.2031-.3105-1.7451.0083l-3.1445 1.8291c-.0693.0425-.1475.043-.2314-.0088l-4.1152-2.3916c-.5518-.3374-1.2334-.3369-1.7695-.0083l-4.125 2.4033c-.084.0498-.1689.0508-.2588-.002l-4.0957-2.3984c-.5591-.3315-1.2271-.332-1.7817-.0024l-4.104 2.4028c-.085.0508-.1699.0508-.2583-.0024l-4.0962-2.3979c-.5581-.332-1.2261-.3325-1.7808-.0024l-4.1147 2.4028c-.0845.0503-.1699.0503-.2573-.002l-3.103-1.8218c-.5513-.3198-1.2095-.3232-1.7603-.0044-.5488.3154-.8765.8813-.8765 1.5132v8.3994c0 .4141.3359.75.75.75s.75-.3359.75-.75v-8.3994c0-.123.0781-.1865.1245-.2129.0474-.0283.1455-.0654.2554-.0005l3.0977 1.8184c.5586.3306 1.2251.332 1.7808.0024l4.1147-2.4028c.0845-.0503.1694-.0503.2578.0024l4.0967 2.3979c.5581.3315 1.2271.3311 1.7812.0024l4.104-2.4028c.0859-.0508.1694-.0498.2583.002l4.0957 2.3984c.5596.3315 1.2275.3315 1.7812.0029l4.1348-2.4092c.0674-.042.1465-.042.2305.0093l4.1143 2.3911c.5498.3369 1.2324.3369 1.7695.0093l3.1338-1.8228c.1055-.062.1982-.0259.2432 0 .0469.0269.126.0903.126.2144v22.1272c-2.756-1.6823-5.9526-2.6174-9.25-2.6174-3.6458 0-7.0366 1.1075-9.8588 3h-14.6412c-.4141 0-.75.3359-.75.75s.3359.75.75.75h12.73c-1.7278 1.5366-3.1494 3.401-4.1741 5.5h-8.556c-.4141 0-.75.3359-.75.75s.3359.75.75.75h7.9078c-.6515 1.7234-1.0387 3.5708-1.1199 5.5h-6.788c-.4141 0-.75.3359-.75.75s.3359.75.75.75h6.788c.0812 1.9292.4684 3.7766 1.1199 5.5h-7.9078c-.4141 0-.75.3359-.75.75s.3359.75.75.75h8.556c1.8426 3.7746 4.9721 6.8046 8.8282 8.5h-24.4387c.803-.851 1.3043-1.9903 1.3043-3.25v-34.3773c0-.4141-.3359-.75-.75-.75s-.75.3359-.75.75v21.6274h-7.25c-.4141 0-.75.3359-.75.75v12c0 2.6191 2.1309 4.75 4.75 4.75h35c9.7871 0 17.75-7.9624 17.75-17.75 0-2.0249-.3389-4.0122-1.0068-5.9062zm-54.9932 18.9062v-11.25h6.5v11.25c0 1.792-1.458 3.25-3.25 3.25s-3.25-1.458-3.25-3.25z">
                                    </path>
                                    <path
                                        d="m51.6621 36.813-10.6064 10.6064c-.0977.0977-.2559.0977-.3535 0l-6.3643-6.3638c-.293-.293-.7676-.293-1.0605 0s-.293.7676 0 1.0605l6.3643 6.3638c.3408.3413.7891.5117 1.2373.5117s.8965-.1704 1.2373-.5117l10.6064-10.6064c.293-.293.293-.7676 0-1.0605s-.7676-.293-1.0605 0z">
                                    </path>
                                </g>
                            </svg>
                            <h6 class="h8">Diagnostic Invoice</h6>
                        </div>
                        <div class="icon-wrap flex-column align-items-center border border-1 p-2 ">
                            <svg id="fi_3082854" enable-background="new 0 0 468 468" height="20"
                                viewBox="0 0 468 468" width="20" xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <g>
                                        <g>
                                            <path
                                                d="m334.419 441.105h-288.984c-11.822 0-21.406-9.597-21.406-21.436v-365.592c0-11.839 9.584-21.436 21.406-21.436h288.984c11.822 0 21.406 9.597 21.406 21.436v365.593c0 11.838-9.584 21.435-21.406 21.435z"
                                                fill="#fff"></path>
                                            <path
                                                d="m66.771 111.124v-78h-21.337c-11.822 0-21.406 9.116-21.406 20.954v365.592c0 11.838 9.584 21.618 21.406 21.618h288.984c11.822 0 21.931-9.779 21.931-21.618v-18.985c-159.929 0-289.578-129.641-289.578-289.561z"
                                                fill="#e6e7e8"></path>
                                            <g fill="#3e4d6c">
                                                <path
                                                    d="m266.833 457.132h-221.398c-20.642 0-37.435-16.805-37.435-37.462v-365.593c0-20.657 16.793-37.463 37.435-37.463h36.259v32.055h-36.259c-2.966 0-5.378 2.426-5.378 5.408v365.593c0 2.982 2.413 5.408 5.378 5.408h221.398z">
                                                </path>
                                                <path
                                                    d="m371.853 272.116h-32.057v-218.039c0-2.982-2.413-5.408-5.378-5.408h-33.625v-32.055h33.625c20.642 0 37.435 16.806 37.435 37.463z">
                                                </path>
                                            </g>
                                        </g>
                                        <path
                                            d="m252.2 64.307h-124.547c-9.941 0-18-8.059-18-18v-28.307c0-9.941 8.059-18 18-18h124.547c9.941 0 18 8.059 18 18v28.307c0 9.941-8.059 18-18 18z"
                                            fill="#0795fe"></path>
                                        <g>
                                            <g>
                                                <path d="m195.308 245.118h115.718v32.055h-115.718z" fill="#3e4d6c"></path>
                                            </g>
                                            <g>
                                                <path d="m195.308 148.658h115.718v32.055h-115.718z" fill="#3e4d6c"></path>
                                            </g>
                                            <g>
                                                <path d="m195.308 341.578h54.811v32.055h-54.811z" fill="#3e4d6c"></path>
                                            </g>
                                        </g>
                                    </g>
                                    <g>
                                        <path id="XMLID_6149_"
                                            d="m290.05 367.087c-8.052 46.599 23.182 91.167 69.763 99.545 46.581 8.379 90.87-22.605 98.922-69.204s-23.182-91.167-69.763-99.545-90.87 22.605-98.922 69.204z"
                                            fill="#0795fe"></path>
                                    </g>
                                    <path
                                        d="m353.927 415.026-29.001-29.524 22.847-22.51 17.534 17.805 35.181-36.031 22.933 22.392-46.603 47.825c-3 3.072-13.344 9.209-22.891.043z"
                                        fill="#fff"></path>
                                    <g fill="#3e4d6c">
                                        <path
                                            d="m93.641 389.156-31.127-33.901 23.624-21.647 19.318 21.073 45.919-50.036 23.617 21.675-57.731 62.839c-3.035 3.307-14.176 8.855-23.62-.003z">
                                        </path>
                                        <path
                                            d="m93.641 292.992-31.127-33.901 23.624-21.647 19.318 21.073 45.919-50.036 23.617 21.675-57.731 62.839c-3.035 3.306-14.176 8.854-23.62-.003z">
                                        </path>
                                        <path
                                            d="m93.641 195.759-31.127-33.901 23.624-21.647 19.318 21.073 45.919-50.036 23.617 21.675-57.731 62.839c-3.035 3.307-14.176 8.854-23.62-.003z">
                                        </path>
                                    </g>
                                </g>
                            </svg>
                            <h6 class="h8">TRF</h6>
                        </div>
                        <div class="icon-wrap flex-column align-items-center border border-1 p-2 ">
                            <svg height="20" id="fi_1828270" viewBox="0 0 512 511" width="20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="m362.667969 512.484375h-298.667969c-35.285156 0-64-28.714844-64-64v-298.667969c0-35.285156 28.714844-64 64-64h170.667969c11.796875 0 21.332031 9.558594 21.332031 21.335938 0 11.773437-9.535156 21.332031-21.332031 21.332031h-170.667969c-11.777344 0-21.332031 9.578125-21.332031 21.332031v298.667969c0 11.753906 9.554687 21.332031 21.332031 21.332031h298.667969c11.773437 0 21.332031-9.578125 21.332031-21.332031v-170.667969c0-11.773437 9.535156-21.332031 21.332031-21.332031s21.335938 9.558594 21.335938 21.332031v170.667969c0 35.285156-28.714844 64-64 64zm0 0"
                                    fill="#607d8b"></path>
                                <g fill="#42a5f5">
                                    <path
                                        d="m368.8125 68.261719-168.792969 168.789062c-1.492187 1.492188-2.496093 3.390625-2.921875 5.4375l-15.082031 75.4375c-.703125 3.496094.40625 7.101563 2.921875 9.640625 2.027344 2.027344 4.757812 3.113282 7.554688 3.113282.679687 0 1.386718-.0625 2.089843-.210938l75.414063-15.082031c2.089844-.429688 3.988281-1.429688 5.460937-2.925781l168.789063-168.789063zm0 0">
                                    </path>
                                    <path
                                        d="m496.382812 16.101562c-20.796874-20.800781-54.632812-20.800781-75.414062 0l-29.523438 29.523438 75.414063 75.414062 29.523437-29.527343c10.070313-10.046875 15.617188-23.445313 15.617188-37.695313s-5.546875-27.648437-15.617188-37.714844zm0 0">
                                    </path>
                                </g>
                            </svg>
                            <h6 class="h8">Edit</h6>
                        </div>
                        <div class="icon-wrap flex-column align-items-center border border-1 p-2 ">
                            <svg height="20" viewBox="0 -3 454.29886 454" width="20"
                                xmlns="http://www.w3.org/2000/svg" id="fi_1251314">
                                <path
                                    d="m267 400.949219-267 .601562c-.199219-95.703125 71.101562-166 159.199219-166.101562 40.800781-.101563 78.101562 14.898437 106.300781 40.601562-28.820312 36.84375-28.195312 88.757813 1.5 124.898438zm0 0"
                                    fill="#e48e66"></path>
                                <path
                                    d="m159.300781.148438c52.851563 0 95.699219 42.847656 95.699219 95.699218 0 52.855469-42.847656 95.703125-95.699219 95.703125-52.855469 0-95.699219-42.847656-95.699219-95.703125 0-52.851562 42.84375-95.699218 95.699219-95.699218zm0 0"
                                    fill="#f8ec7d"></path>
                                <path
                                    d="m159.300781 235.347656h-.199219c-88 .203125-159.300781 70.5-159.101562 166.101563l159.300781-.300781zm0 0"
                                    fill="#d18162"></path>
                                <path
                                    d="m159.300781.148438c-52.855469 0-95.699219 42.847656-95.699219 95.699218 0 52.855469 42.84375 95.703125 95.699219 95.703125zm0 0"
                                    fill="#e2d574"></path>
                                <path
                                    d="m344.300781 227.449219c-33.839843-.078125-65.808593 15.5-86.601562 42.199219l-.097657.101562c-15.15625 19.304688-23.367187 43.15625-23.300781 67.699219.027344 25.453125 8.859375 50.117187 25 69.800781 1.601563 1.898438 3.300781 3.800781 5 5.699219 34.441407 36.496093 89.21875 45.054687 133.152344 20.804687 43.933594-24.246094 65.886719-75.152344 53.367187-123.746094-12.519531-48.597656-56.339843-82.558593-106.519531-82.558593zm0 0"
                                    fill="#63316d"></path>
                                <path
                                    d="m328.402344 370.148438c-2.921875-.007813-5.695313-1.285157-7.601563-3.5l-24-28.097657c-3.589843-4.199219-3.097656-10.511719 1.101563-14.101562 4.195312-3.589844 10.507812-3.097657 14.097656 1.101562l17.699219 20.699219 48.300781-39.300781c4.28125-3.507813 10.59375-2.878907 14.101562 1.398437 3.507813 4.28125 2.878907 10.59375-1.402343 14.101563l-55.898438 45.5c-1.835937 1.414062-4.082031 2.1875-6.398437 2.199219zm0 0"
                                    fill="#f8ec7d"></path>
                            </svg>
                            <h6 class="h8">Phlebo Assign</h6>
                        </div>
                        <div class="icon-wrap flex-column align-items-center border border-1 p-2 ">
                            <svg id="fi_17938363" height="20" width="20" enable-background="new 0 0 100 100"
                                viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                <g fill="#f46700">
                                    <path
                                        d="m50 10.4166689c-11.0833282 0-20.5833378 4.7500029-27.7083321 11.874999l11.0833282 11.0833282c3.9583321-3.958332 10.2916718-7.1249942 16.6250039-7.1249942 13.4583359 0 23.75 10.2916698 23.75 23.7499981h-7.9166641l15.8333282 15.8333359 15.8333359-15.8333359h-7.9166641c0-22.1666641-17.4166718-39.5833311-39.5833359-39.5833311z">
                                    </path>
                                    <path
                                        d="m50 73.75c-13.4583359 0-23.75-10.2916603-23.75-23.75h7.9166679l-15.8333339-15.8333321-15.8333333 15.8333321h7.916666c0 22.1666565 17.4166679 39.5833282 39.5833321 39.5833282 11.0833282 0 20.5833359-4.7499924 27.7083359-11.8749924l-11.0833347-11.0833358c-3.9583282 4.7500076-10.2916679 7.125-16.625 7.125z">
                                    </path>
                                </g>
                            </svg>
                            <h6 class="h8">Phlebo Reassign</h6>
                        </div>
                    </div>
                </div>
                <div class="Table-custom-padding1 table-responsive">
                    <table id="data-list" class="table table-sm datatable_desc placeholder-glow"
                        data-toggle="data-table">
                        <thead>
                            <tr>
                                <th>
                                    ID
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="0" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="0" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Workorder-ID
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="1" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="1" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Patient Name
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="2" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="2" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Doctor
                                </th>
                                <th>
                                    Clinic
                                </th>
                                <th>
                                    Visit Date-Time
                                </th>
                                <th>
                                    Collection
                                </th>
                                <th>
                                    Billed Amt (₹)
                                </th>
                                <th>
                                    Due Amt (₹)
                                </th>
                                <th>
                                    Phlebotomist
                                </th>
                                <th>
                                    Status
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php echo $__env->make('admin.custom.loading', ['td' => 12, 'action' => 6], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tbody>
                        <tfoot>
                            <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
    <div class="modal fade" id="uploadPrescriptionModal" tabindex="-1"
        aria-labelledby="exampleModalCenteredScrollableTitle" aria-hidden="true">
        <div class="modal-dialog  modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header pb-0 border-0">
                    <h5 class="modal-title">Upload Prescription <!--| <span id="header_doc"></span>--></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="new-user-info">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="card mb-0">
                                    <div class="card-body p-0">
                                        <form class="clearfix" method="post"
                                            action="<?php echo e(config('diagnostic.url') . 'uploadPrescription'); ?>"
                                            data-mode="add" enctype="multipart/form-data" id="submitForm">
                                            <div class="row justify-content-center">
                                                <div class="form-group col-md-11 mt-3 mb-3">
                                                    <input type="hidden" class="form-control" name="sample_id"
                                                        id="sample_modal_id" value="" placeholder=""
                                                        required="">
                                                    <label for="exampleInputEmail1" class="fw-bold mb-2">Upload
                                                        Prescription</label>
                                                    <input type="file" name="prescription_upload_file"
                                                        class="form-control form-control-sm"
                                                        aria-label="Large file input example">
                                                </div>
                                            </div>
                                            <div class="modal-footer border-0">
                                                <button type="button" class="btn btn-gray"
                                                    data-bs-dismiss="modal">Close</button>
                                                <button type="submit" name="submit"
                                                    class="btn btn-primary text-white">Submit</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- </form> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).on("submit", "#submitForm", function() {
            event.preventDefault(); // Prevent the form from submitting normally
            var form = $(this).closest('form');
            setFormEntity(form);
            addUpdateForm();
        });
        function resChildHtml(data) {
            $('#uploadPrescriptionModal').modal('hide');
            getList();
        }
        function uploadPrescription(sample_id) {
            $('#sample_modal_id').val(sample_id);
        }

        function toggleTooltip(element) {
            // Toggle the 'show-tooltip' class on mobile only
            if (window.innerWidth <= 768) {
                element.classList.toggle("show-tooltip");
            }
        }
        $("#date_range").flatpickr({
            mode: "range",
            // minDate: "today"
        });
        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "<?php echo e(config('diagnostic.url') . 'list'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [
                "id",
                "unique_id",
                "patients.name",
                "patient_phone",
                // "clinics.clinic_name",
            ];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {

                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 3,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });

        function updateDiagnosticStatus(url, method, parent) {
            // console.log(url,method);
            if (!jwtToken) {
                redirectToSwal(loginUrl);
                return false;
            }
            // let field = $(parent).attr('name');
            let status = $(parent).val();
            // console.log(field, status);
            // return false;
            $.ajax({
                type: method,
                url: url,
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    status: status
                }), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                success: function(data) {
                    // console.log(data);
                    if (data.success == true) {
                        successAlertSwal(data);
                    } else {
                        errorAlertSwal(data);
                    }
                    getList();
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Diagnostic\resources/views/diagnostic/index.blade.php ENDPATH**/ ?>