<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td><?php echo e($row['patient_name']); ?></td>
        <td><?php echo e($row['patient_phone']); ?></td>
        <td>
            <table class="table table-sm m-0">
                <?php if(count($row['order_child_medicines']) > 0): ?>
                    <?php $__currentLoopData = $row['order_child_medicines']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="word-wrap wht-space-custom w-75"><?php echo e($row2['medicine']['name']); ?></td>
                            <td class="w-25 text-center"><?php echo e($row2['quantity']); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </table>
            <div class="dropdown position-static">
                <button class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                    type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                    <svg xmlns="http://www.w3.org/2000/svg" height="16" version="1.1" viewBox="-23 0 512 512.00072"
                        width="16" id="fi_1092000">
                        <g id="surface1">
                            <path
                                d="M 348.945312 221.640625 L 348.945312 124.746094 C 348.945312 121.972656 347.664062 119.410156 345.851562 117.382812 L 237.21875 3.308594 C 235.191406 1.175781 232.308594 0 229.429688 0 L 57.195312 0 C 25.398438 0 0 25.929688 0 57.730469 L 0 383.414062 C 0 415.214844 25.398438 440.71875 57.195312 440.71875 L 193.148438 440.71875 C 218.863281 483.402344 265.605469 512 318.851562 512 C 399.738281 512 465.792969 446.265625 465.792969 365.273438 C 465.902344 294.523438 415.105469 235.40625 348.945312 221.640625 Z M 240.101562 37.457031 L 312.984375 114.179688 L 265.710938 114.179688 C 251.625 114.179688 240.101562 102.550781 240.101562 88.464844 Z M 57.195312 419.375 C 37.242188 419.375 21.34375 403.367188 21.34375 383.414062 L 21.34375 57.730469 C 21.34375 37.667969 37.242188 21.34375 57.195312 21.34375 L 218.757812 21.34375 L 218.757812 88.464844 C 218.757812 114.394531 239.78125 135.523438 265.710938 135.523438 L 327.601562 135.523438 L 327.601562 218.863281 C 324.402344 218.757812 321.839844 218.4375 319.066406 218.4375 C 281.824219 218.4375 247.570312 232.738281 221.746094 255.148438 L 86.222656 255.148438 C 80.351562 255.148438 75.550781 259.949219 75.550781 265.816406 C 75.550781 271.6875 80.351562 276.488281 86.222656 276.488281 L 201.898438 276.488281 C 194.320312 287.160156 188.023438 297.832031 183.117188 309.570312 L 86.222656 309.570312 C 80.351562 309.570312 75.550781 314.371094 75.550781 320.242188 C 75.550781 326.109375 80.351562 330.914062 86.222656 330.914062 L 176.179688 330.914062 C 173.511719 341.585938 172.125 353.429688 172.125 365.273438 C 172.125 384.480469 175.859375 403.476562 182.582031 419.484375 L 57.195312 419.484375 Z M 318.960938 490.765625 C 249.8125 490.765625 193.574219 434.527344 193.574219 365.378906 C 193.574219 296.230469 249.703125 239.992188 318.960938 239.992188 C 388.214844 239.992188 444.34375 296.230469 444.34375 365.378906 C 444.34375 434.527344 388.109375 490.765625 318.960938 490.765625 Z M 318.960938 490.765625 "
                                style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                fill="currentColor"></path>
                            <path
                                d="M 86.222656 223.027344 L 194.320312 223.027344 C 200.191406 223.027344 204.992188 218.222656 204.992188 212.355469 C 204.992188 206.484375 200.191406 201.683594 194.320312 201.683594 L 86.222656 201.683594 C 80.351562 201.683594 75.550781 206.484375 75.550781 212.355469 C 75.550781 218.222656 80.351562 223.027344 86.222656 223.027344 Z M 86.222656 223.027344 "
                                style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                fill="currentColor"></path>
                            <path
                                d="M 373.59375 363.136719 L 329.738281 410.410156 L 329.738281 293.882812 C 329.738281 288.011719 324.933594 283.210938 319.066406 283.210938 C 313.195312 283.210938 308.394531 288.011719 308.394531 293.882812 L 308.394531 410.410156 L 264.214844 363.136719 C 260.160156 358.871094 253.332031 358.550781 249.0625 362.605469 C 244.792969 366.660156 244.472656 373.382812 248.53125 377.652344 L 310.957031 444.773438 C 312.984375 446.90625 315.757812 448.1875 318.746094 448.1875 C 321.734375 448.1875 324.507812 446.90625 326.535156 444.773438 L 389.070312 377.652344 C 393.125 373.382812 392.910156 366.554688 388.640625 362.605469 C 384.265625 358.550781 377.652344 358.871094 373.59375 363.136719 Z M 373.59375 363.136719 "
                                style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                fill="currentColor"></path>
                        </g>
                    </svg>
                </button>
                <ul class="dropdown-menu bg-light p-0" aria-labelledby="dropdownMenuButton1" style="">
                    <?php
                        try {
                            $prescription_upload = $row['prescription_upload'];
                            if (!str_starts_with($prescription_upload, 'https://storage.googleapis.com')) {
                                $prescription_upload = Storage::disk('gcs')->url($prescription_upload);
                            }
                        } catch (\Throwable $th) {
                            $prescription_upload = '';
                        }
                    ?>
                    <?php if($prescription_upload != '' && $row['prescription_upload']): ?>
                        <li>
                            <a class="dropdown-item h6 border-bottom" target="_blank" href="<?php echo e($prescription_upload); ?>">
                                <svg version="1.1" id="fi_159604" height="16" width="16"
                                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                    x="0px" y="0px" viewBox="0 0 488.85 488.85"
                                    style="enable-background:new 0 0 488.85 488.85;" xml:space="preserve">
                                    <g>
                                        <path
                                            d="M244.425,98.725c-93.4,0-178.1,51.1-240.6,134.1c-5.1,6.8-5.1,16.3,0,23.1c62.5,83.1,147.2,134.2,240.6,134.2
                                            s178.1-51.1,240.6-134.1c5.1-6.8,5.1-16.3,0-23.1C422.525,149.825,337.825,98.725,244.425,98.725z M251.125,347.025
                                            c-62,3.9-113.2-47.2-109.3-109.3c3.2-51.2,44.7-92.7,95.9-95.9c62-3.9,113.2,47.2,109.3,109.3
                                            C343.725,302.225,302.225,343.725,251.125,347.025z M248.025,299.625c-33.4,2.1-61-25.4-58.8-58.8c1.7-27.6,24.1-49.9,51.7-51.7
                                            c33.4-2.1,61,25.4,58.8,58.8C297.925,275.625,275.525,297.925,248.025,299.625z">
                                        </path>
                                    </g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                    <g></g>
                                </svg>
                                <small class="ms-2">View</small>
                            </a>
                        </li>
                    <?php endif; ?>
                    <li>
                        <a class="dropdown-item h6" href="#" onclick="uploadPrescription(<?php echo e($row['id']); ?>)">
                            <svg xmlns="http://www.w3.org/2000/svg" id="fi_3097412" data-name="Layer 2" width="16"
                                height="16" viewBox="0 0 24 24">
                                <path
                                    d="M22,13a1,1,0,0,0-1,1v4.213A2.79,2.79,0,0,1,18.213,21H5.787A2.79,2.79,0,0,1,3,18.213V14a1,1,0,0,0-2,0v4.213A4.792,4.792,0,0,0,5.787,23H18.213A4.792,4.792,0,0,0,23,18.213V14A1,1,0,0,0,22,13Z">
                                </path>
                                <path
                                    d="M6.707,8.707,11,4.414V17a1,1,0,0,0,2,0V4.414l4.293,4.293a1,1,0,0,0,1.414-1.414l-6-6a1,1,0,0,0-1.414,0l-6,6A1,1,0,0,0,6.707,8.707Z">
                                </path>
                            </svg>
                            <small class="ms-2">Upload</small>
                        </a>
                    </li>
                </ul>
            </div>
        </td>
        <td><?php echo e($row['clinic_name']); ?></td>
        <td class="word-wrap wht-space-custom">
            <?php if($row['type_of_collection'] == 'CV'): ?>
                Store Pickup
            <?php else: ?>
                <h6 class="h7 fw-bold">Home Delivery</h6>
                <p class="fst-italic mb-0 mt-1"><?php echo e($row['full_address']); ?>, <?php echo e($row['landmark']); ?>, <?php echo e($row['city']); ?>,
                    <?php echo e($row['pincode']); ?></p>
            <?php endif; ?>
        </td>
        <td><?php echo e($row['total_amount']); ?></td>
        <td>
            <?php echo e(config('pharmacy.data_source_list')[$row['data_source']]); ?>

        </td>
        <td>
            
            <?php echo e($row['status'] == 6 ? 'Rejected' : $status_list[$row['status']]); ?>

            <?php if($row['status'] == 6 && $row['reject_remarks']): ?>
                <br>
                <?php echo e($row['reject_remarks']); ?>

            <?php endif; ?>
            
        </td>
        <td>
            <div class="d-flex flex-md-nowrap gap-2 align-items-center justify-content-end">
                <div class="d-flex flex-md-nowrap gap-1 mx-2">
                    <?php if(array_intersect(['edit_pharmacy_order'], $permissionPage)): ?>
                        <a href="<?php echo e(route('pharmacy.order.addForm', ['id' => $row['id']])); ?>"
                            class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                            title="Edit" role="button">
                            <svg width="32" class="icon-16" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.4"
                                    d="M19.9927 18.9534H14.2984C13.7429 18.9534 13.291 19.4124 13.291 19.9767C13.291 20.5422 13.7429 21.0001 14.2984 21.0001H19.9927C20.5483 21.0001 21.0001 20.5422 21.0001 19.9767C21.0001 19.4124 20.5483 18.9534 19.9927 18.9534Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M10.309 6.90385L15.7049 11.2639C15.835 11.3682 15.8573 11.5596 15.7557 11.6929L9.35874 20.0282C8.95662 20.5431 8.36402 20.8344 7.72908 20.8452L4.23696 20.8882C4.05071 20.8903 3.88775 20.7613 3.84542 20.5764L3.05175 17.1258C2.91419 16.4915 3.05175 15.8358 3.45388 15.3306L9.88256 6.95545C9.98627 6.82108 10.1778 6.79743 10.309 6.90385Z"
                                    fill="currentColor"></path>
                                <path opacity="0.4"
                                    d="M18.1208 8.66544L17.0806 9.96401C16.9758 10.0962 16.7874 10.1177 16.6573 10.0124C15.3927 8.98901 12.1545 6.36285 11.2561 5.63509C11.1249 5.52759 11.1069 5.33625 11.2127 5.20295L12.2159 3.95706C13.126 2.78534 14.7133 2.67784 15.9938 3.69906L17.4647 4.87078C18.0679 5.34377 18.47 5.96726 18.6076 6.62299C18.7663 7.3443 18.597 8.0527 18.1208 8.66544Z"
                                    fill="currentColor"></path>
                            </svg>
                        </a>
                    <?php endif; ?>
                </div>
                <div class="d-flex flex-md-nowrap gap-2">
                    <?php if(array_intersect(['change_status_pharmacy_order'], $permissionPage)): ?>
                        <?php
                            $arr_address = [
                                'type_of_collection' => $row['type_of_collection'],
                                'full_address' => $row['full_address'],
                                'landmark' => $row['landmark'],
                                'city' => $row['city'],
                                'pincode' => $row['pincode'],
                            ];
                        ?>
                        <button type="button"
                            class="btn btn-sm btn-outline-success <?php echo e($row['is_accept'] == 1 ? 'active' : ''); ?>"
                            data-bs-toggle="modal" data-bs-target="#medicineAcceptModal"
                            onclick="medicineAcceptModal(this,<?php echo e($row['id']); ?>)"
                            data-id="<?php echo e(json_encode($arr_address)); ?>">
                            Accept
                        </button>
                        <button type="button"
                            class="btn btn-sm btn-outline-primary <?php echo e($row['is_accept'] == 2 ? 'active' : ''); ?>"
                            data-bs-toggle="modal" data-bs-target="#medicineRejectModal"
                            onclick="medicineRejectModal(this,<?php echo e($row['id']); ?>)">
                            Reject
                        </button>
                    <?php endif; ?>

                </div>
            </div>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Pharmacy\resources/views/order/api/list.blade.php ENDPATH**/ ?>