<aside class="sidebar sidebar-default sidebar-white sidebar-base navs-rounded-all ">
    <div class="sidebar-header d-flex align-items-center justify-content-start">
        <a href="<?php echo e(route('user.dashboard')); ?>" class="navbar-brand">
            <!--Logo start-->
            <!--logo End-->

            <!--Logo start-->
            <div
                class="logo-main bg-white position-relative z-1 d-flex align-items-center justify-content-center flex-shrink-0">
                <div class="logo-normal">

                    <svg class="icon-30" id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 19.23 19.24">
                        <g id="Layer_1-2" data-name="Layer 1">
                            <g>
                                <line fill="#565355" x1="12.44" y1="19.24" x2="12.45" y2="12.45" />
                                <rect fill="#dd2a1b" x="6.77" width="5.68" height="19.23" />
                                <rect fill="#dd2a1b" y="6.77" width="19.23" height="5.68" />
                                <polygon fill="#565355"
                                    points="12.44 6.78 19.22 6.78 19.23 12.45 12.45 12.45 12.45 19.23 6.77 19.23 6.77 12.45 12.44 6.78" />
                                <polygon fill="#565355" points="14.23 19.23 14.22 14.23 19.22 14.23 14.23 19.23" />
                            </g>
                        </g>
                    </svg>
                </div>
                <div class="logo-mini">
                    <svg class="icon-30" id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 19.23 19.24">
                        <g id="Layer_1-2" data-name="Layer 1">
                            <g>
                                <line fill="#565355" x1="12.44" y1="19.24" x2="12.45" y2="12.45" />
                                <rect fill="#dd2a1b" x="6.77" width="5.68" height="19.23" />
                                <rect fill="#dd2a1b" y="6.77" width="19.23" height="5.68" />
                                <polygon fill="#565355"
                                    points="12.44 6.78 19.22 6.78 19.23 12.45 12.45 12.45 12.45 19.23 6.77 19.23 6.77 12.45 12.44 6.78" />
                                <polygon fill="#565355" points="14.23 19.23 14.22 14.23 19.22 14.23 14.23 19.23" />
                            </g>
                        </g>
                    </svg>
                </div>
            </div>
            <!--logo End-->




            <div class="logo-title flex-grow-1">
                <!--Logo start-->
                <!--logo End-->

                <!--Logo start-->
                <div class="logo-main-full">
                    <svg id="Layer_1" class="icon-40 w-auto" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"
                        width="181.72" height="65.04" viewBox="0 0 181.72 65.04">
                        <g id="Group_1" data-name="Group 1">
                            <rect id="Rectangle_1" data-name="Rectangle 1" width="5.68" height="19.23"
                                transform="translate(159.35)" fill="#dd2a1b" />
                            <rect id="Rectangle_2" data-name="Rectangle 2" width="19.23" height="5.68"
                                transform="translate(152.58 6.77)" fill="#dd2a1b" />
                            <path id="Path_1" data-name="Path 1"
                                d="M131.91,14.93h-2.27l.1,18h.94c6.95,0,11.33-3.08,12.12-8.99,0-5.84-4.76-9.01-10.88-9.01h0Zm-.74-8.47V6.43h.76a22.559,22.559,0,0,1,14.86,5.15,16.474,16.474,0,0,1,4.93,12.4,16.338,16.338,0,0,1-4.93,12.36c-4.33,3.41-9.88,5.12-16.38,5.12H120.32v-35Z"
                                fill="#dd2b1e" />
                            <path id="Path_2" data-name="Path 2" d="M69.57,33.95l-3.16,7.51H83.33V33.95Z"
                                fill="#dd2b1e" fill-rule="evenodd" />
                            <path id="Path_3" data-name="Path 3"
                                d="M77.55,6.33H70.57L64.73,20.29,59.37,6.33H52.38L61.2,28.54,55.68,41.46h7.17l5.44-12.95Z"
                                fill="#565355" />
                            <path id="Path_4" data-name="Path 4"
                                d="M42.75,6.3c-7.49,0-8.61,6.15-8.63,6.22l-.06.38-.11-.36c-.02-.06-1.89-6.06-7.87-6.22h-.27a7.658,7.658,0,0,0-7.41,5.23l-.19.62V6.43H11.56V29.59h6.61c.06-1.48.06-2.67.05-4.17,0-.63-.02-1.3-.02-2.06,0-.62-.02-1.29-.05-1.98-.08-2.39-.17-4.87.67-6.53a4.48,4.48,0,0,1,4.08-2.38,4.742,4.742,0,0,1,4.46,2.79c.67,1.59.57,3.87.48,6.07-.03.73-.06,1.51-.06,2.21v6.03c.68.03,1.44.05,2.4.05.65,0,1.3-.01,1.97-.01s1.33-.02,2-.02h.38V24.21c0-.81-.03-1.62-.06-2.41-.11-4-.24-7.77,2.97-9.01a5.542,5.542,0,0,1,1.97-.4,4.722,4.722,0,0,1,4.58,3.98,25.031,25.031,0,0,1,.16,5.39c-.03.81-.06,1.65-.06,2.44v5.39h6.57c.02-1.63.05-3.21.08-4.71.06-3.05.13-5.89,0-8.87-.11-2.92-1.14-9.71-7.96-9.71h0Z"
                                fill="#565355" fill-rule="evenodd" />
                            <path id="Path_5" data-name="Path 5" d="M55.36,33.95H11.53v7.51H52.16Z" fill="#dd2b1e"
                                fill-rule="evenodd" />
                            <path id="Path_6" data-name="Path 6"
                                d="M107.59,6.23l-8.1,10.44L91.38,6.23H82.26V41.46h8.63V18.73l8.6,10.19,8.55-10.22V41.46h8.64V6.23Z"
                                fill="#dd2b1e" />
                            <path id="Path_7" data-name="Path 7"
                                d="M1.27,53.35v3.16h1a2.137,2.137,0,0,0,1.46-.35,1.7,1.7,0,0,0,.36-1.25,1.752,1.752,0,0,0-.33-1.21,1.6,1.6,0,0,0-1.16-.35H1.27Zm1.35-1.08a3.023,3.023,0,0,1,2.14.6,2.728,2.728,0,0,1,.62,2.05,2.89,2.89,0,0,1-.6,2.01,2.452,2.452,0,0,1-1.87.65H1.28v4.28H0v-9.6H2.62Zm3.3,6.25a4.685,4.685,0,0,1,.6-2.66,2.224,2.224,0,0,1,1.92-.82,2.137,2.137,0,0,1,1.9.82,4.6,4.6,0,0,1,.6,2.66,4.76,4.76,0,0,1-.6,2.68,2.184,2.184,0,0,1-1.9.82A2.262,2.262,0,0,1,6.5,61.2a4.686,4.686,0,0,1-.59-2.68h0Zm2.52-2.5a1.056,1.056,0,0,0-1.03.59,4.122,4.122,0,0,0-.32,1.92,4.787,4.787,0,0,0,.3,1.97,1.114,1.114,0,0,0,1.05.57,1.034,1.034,0,0,0,1.01-.57,4.437,4.437,0,0,0,.32-1.95,4.377,4.377,0,0,0-.32-1.95,1.068,1.068,0,0,0-1.02-.57h0Zm4.95,5.85H12.25v-9.6h1.14Zm3.57,2.54H15.82l.75-2.54-2.3-6.69h1.35l1.6,5.3,1.59-5.3h1.24l-3.08,9.23Zm7.58-4.79H25.7a2.479,2.479,0,0,1-.67,1.78,2.3,2.3,0,0,1-1.74.64,2.219,2.219,0,0,1-1.92-.81,4.656,4.656,0,0,1-.6-2.68,4.737,4.737,0,0,1,.6-2.68,2.224,2.224,0,0,1,1.92-.82,2.577,2.577,0,0,1,1.73.57,1.9,1.9,0,0,1,.65,1.51v.14H24.54a1.27,1.27,0,0,0-.32-.92,1.125,1.125,0,0,0-.87-.32,1.107,1.107,0,0,0-1.08.56,4.143,4.143,0,0,0-.33,1.97,4.26,4.26,0,0,0,.32,1.95,1.093,1.093,0,0,0,1.03.57,1.122,1.122,0,0,0,.9-.4,1.58,1.58,0,0,0,.35-1.05h0Zm3.52,2.25H26.92v-9.6h1.14Zm1.59-8.36V52.27h1.09v1.24Zm0,8.36V55.18h1.09v6.69Zm6.25,0V57.6a2.457,2.457,0,0,0-.25-1.33.932.932,0,0,0-.86-.37,1.1,1.1,0,0,0-1,.51,3.125,3.125,0,0,0-.3,1.6v3.85H32.33V56.23a4.186,4.186,0,0,0-.02-.48,4.683,4.683,0,0,0-.05-.59h1.17l.06.78a2.269,2.269,0,0,1,.78-.71,2.053,2.053,0,0,1,.98-.22,1.864,1.864,0,0,1,1.32.49,1.784,1.784,0,0,1,.48,1.32v5.04H35.89Zm2.7-8.36V52.27h1.11v1.24Zm0,8.36V55.18h1.11v6.69Zm6.31-2.25h1.14a2.387,2.387,0,0,1-.65,1.78,2.32,2.32,0,0,1-1.76.64,2.192,2.192,0,0,1-1.9-.81,4.656,4.656,0,0,1-.6-2.68,4.737,4.737,0,0,1,.6-2.68,2.224,2.224,0,0,1,1.92-.82,2.488,2.488,0,0,1,1.71.57,1.886,1.886,0,0,1,.67,1.51v.14H44.9a1.307,1.307,0,0,0-.3-.92,1.353,1.353,0,0,0-1.97.24,4.3,4.3,0,0,0-.32,1.97,4.26,4.26,0,0,0,.32,1.95,1.073,1.073,0,0,0,1.03.57,1.1,1.1,0,0,0,.89-.4,1.589,1.589,0,0,0,.37-1.05h0Zm8.61-7.93V65.03H52.7V51.69h.82Zm8.26,1.67v3.16h1a2.137,2.137,0,0,0,1.46-.35,1.651,1.651,0,0,0,.38-1.25,1.862,1.862,0,0,0-.33-1.21,1.649,1.649,0,0,0-1.17-.35H61.79Zm1.35-1.08a3.115,3.115,0,0,1,2.16.6,2.773,2.773,0,0,1,.6,2.05,2.842,2.842,0,0,1-.59,2.01,2.486,2.486,0,0,1-1.89.65H61.78v4.28H60.51v-9.6h2.62Zm7.52,9.6V57.61a2.663,2.663,0,0,0-.25-1.33.977.977,0,0,0-.86-.37,1.085,1.085,0,0,0-1,.51,3.1,3.1,0,0,0-.32,1.6v3.85H67.08v-9.6h1.14v3.68a2.854,2.854,0,0,1,.79-.71,2.135,2.135,0,0,1,1-.22,1.781,1.781,0,0,1,1.3.49,1.75,1.75,0,0,1,.49,1.32v5.04H70.64Zm3.71-4.93H73.14v-.14a1.534,1.534,0,0,1,.6-1.32,2.888,2.888,0,0,1,1.76-.46,2.531,2.531,0,0,1,1.71.48,1.876,1.876,0,0,1,.54,1.48v3.12c0,.3.02.6.03.9.03.3.05.59.1.87H76.74l-.08-.84a2.518,2.518,0,0,1-.79.68,2,2,0,0,1-.95.24,1.788,1.788,0,0,1-1.38-.57,2.35,2.35,0,0,1,.21-3.22,3.544,3.544,0,0,1,2.24-.59h.63v-.33a2.085,2.085,0,0,0-.22-1.1,1.006,1.006,0,0,0-.81-.28,1.4,1.4,0,0,0-.9.25.852.852,0,0,0-.32.74v.08h0Zm2.27,1.43a4.009,4.009,0,0,0-1.93.33,1.147,1.147,0,0,0-.54,1.08,1.208,1.208,0,0,0,.29.82,1.007,1.007,0,0,0,.76.33,1.162,1.162,0,0,0,1.05-.54,2.59,2.59,0,0,0,.38-1.51v-.52h0Zm3.65-3.19.11.87a1.466,1.466,0,0,1,.62-.76,1.858,1.858,0,0,1,1.06-.27h.22V56.2h-.3a1.69,1.69,0,0,0-1.24.37,1.8,1.8,0,0,0-.36,1.27v4.04H79.25V56.31s-.02-.32-.06-.76c-.03-.16-.03-.29-.05-.36Zm3.98.78a2.462,2.462,0,0,1,.78-.71,2.135,2.135,0,0,1,1-.22,2.008,2.008,0,0,1,1.08.3,1.636,1.636,0,0,1,.64.82,2.009,2.009,0,0,1,.76-.84,2.094,2.094,0,0,1,1.1-.28,1.7,1.7,0,0,1,1.3.49,1.784,1.784,0,0,1,.48,1.32v5.04H90.26V57.62a2.457,2.457,0,0,0-.25-1.33.932.932,0,0,0-.86-.37,1.118,1.118,0,0,0-1,.51,3.125,3.125,0,0,0-.3,1.6v3.85H86.68V57.61a2.577,2.577,0,0,0-.24-1.33.977.977,0,0,0-.86-.37,1.085,1.085,0,0,0-1,.51,3.1,3.1,0,0,0-.32,1.6v3.85H83.12V56.24c0-.14,0-.3-.01-.48s-.03-.36-.05-.59h1.17l.05.78Zm9.69.98H92.73v-.14a1.586,1.586,0,0,1,.6-1.32,2.893,2.893,0,0,1,1.78-.46,2.484,2.484,0,0,1,1.7.48,1.884,1.884,0,0,1,.56,1.48v3.12c0,.3,0,.6.03.9s.05.59.08.87H96.34l-.08-.84a2.316,2.316,0,0,1-.79.68,1.933,1.933,0,0,1-.94.24,1.862,1.862,0,0,1-1.4-.57,2.1,2.1,0,0,1-.52-1.49,2.025,2.025,0,0,1,.75-1.73,3.481,3.481,0,0,1,2.22-.59h.64v-.33a1.906,1.906,0,0,0-.22-1.1.988.988,0,0,0-.79-.28,1.422,1.422,0,0,0-.92.25.878.878,0,0,0-.32.74v.08Zm2.27,1.43a4.083,4.083,0,0,0-1.94.33,1.147,1.147,0,0,0-.54,1.08,1.168,1.168,0,0,0,.3.82.943.943,0,0,0,.75.33,1.189,1.189,0,0,0,1.06-.54,2.674,2.674,0,0,0,.36-1.51v-.52h0Zm6.23,1.25h1.14a2.231,2.231,0,0,1-2.41,2.42,2.192,2.192,0,0,1-1.9-.81,4.656,4.656,0,0,1-.6-2.68,4.737,4.737,0,0,1,.6-2.68,2.224,2.224,0,0,1,1.92-.82,2.47,2.47,0,0,1,1.71.57,1.886,1.886,0,0,1,.67,1.51v.14h-1.14a1.307,1.307,0,0,0-.3-.92,1.353,1.353,0,0,0-1.97.24,4.3,4.3,0,0,0-.32,1.97,4.26,4.26,0,0,0,.32,1.95,1.073,1.073,0,0,0,1.03.57,1.1,1.1,0,0,0,.89-.4,1.589,1.589,0,0,0,.37-1.05h0Zm4.49,4.79H105.8l.75-2.54-2.3-6.69h1.35l1.6,5.3,1.59-5.3h1.24l-3.08,9.23ZM117.12,51.7V65.04h-.82V51.7h.82Zm7,10.18v-9.6h2.3a7.214,7.214,0,0,1,1.85.16,2.05,2.05,0,0,1,.92.52,2.471,2.471,0,0,1,.7,1.24,15.106,15.106,0,0,1,.19,3,16.425,16.425,0,0,1-.13,2.36,3.542,3.542,0,0,1-.44,1.22,2.435,2.435,0,0,1-1.01.86,6.31,6.31,0,0,1-2.08.24h-2.3Zm1.28-1.1h1.35a3.634,3.634,0,0,0,1.1-.14,1.313,1.313,0,0,0,.62-.51,3.351,3.351,0,0,0,.29-1.06,17.269,17.269,0,0,0,.1-2.06c0-.63-.01-1.21-.06-1.7a4.944,4.944,0,0,0-.12-.92,1.432,1.432,0,0,0-.64-.82,2.648,2.648,0,0,0-1.27-.24h-1.35v7.46Zm6.2-7.26V52.28h1.11v1.24Zm0,8.36V55.19h1.11v6.69Zm3.81-4.93h-1.24v-.14a1.586,1.586,0,0,1,.6-1.32,2.934,2.934,0,0,1,1.78-.46,2.531,2.531,0,0,1,1.71.48,1.842,1.842,0,0,1,.54,1.48v3.12c0,.3.02.6.03.9s.05.59.08.87h-1.14l-.08-.84a2.317,2.317,0,0,1-.79.68,1.906,1.906,0,0,1-.94.24,1.862,1.862,0,0,1-1.4-.57,2.381,2.381,0,0,1,.23-3.22,3.481,3.481,0,0,1,2.22-.59h.65v-.33a1.883,1.883,0,0,0-.24-1.1.961.961,0,0,0-.79-.28,1.422,1.422,0,0,0-.92.25.867.867,0,0,0-.3.74v.08h0Zm2.27,1.43a4.029,4.029,0,0,0-1.94.33,1.153,1.153,0,0,0-.55,1.08,1.168,1.168,0,0,0,.3.82.963.963,0,0,0,.76.33,1.2,1.2,0,0,0,1.05-.54,2.59,2.59,0,0,0,.38-1.51v-.52Zm3.62.14a4.648,4.648,0,0,0,.3,1.95,1.007,1.007,0,0,0,.98.6,1.166,1.166,0,0,0,1.06-.6,4.328,4.328,0,0,0,.33-1.95,4.576,4.576,0,0,0-.32-2.03,1.084,1.084,0,0,0-1.03-.62,1.056,1.056,0,0,0-1.03.59,4.707,4.707,0,0,0-.3,2.06h0Zm-.78,4.11h1.3a1.077,1.077,0,0,0,.27.73.9.9,0,0,0,.7.27,1.088,1.088,0,0,0,.89-.32,2.054,2.054,0,0,0,.25-1.22v-.97a2.661,2.661,0,0,1-.74.7,1.647,1.647,0,0,1-.86.22,1.838,1.838,0,0,1-1.68-.86,5.251,5.251,0,0,1-.54-2.73,4.73,4.73,0,0,1,.54-2.6,1.816,1.816,0,0,1,1.66-.82,1.929,1.929,0,0,1,.9.19,2.2,2.2,0,0,1,.71.62l.06-.65h1.16c-.02.16-.03.33-.05.52s-.01.41-.01.67v5.87a3.346,3.346,0,0,1-.1.95,1.586,1.586,0,0,1-.28.6,1.631,1.631,0,0,1-.76.54,3.513,3.513,0,0,1-1.17.16,2.441,2.441,0,0,1-1.63-.49,1.785,1.785,0,0,1-.62-1.38h0Zm9.63-.74V57.62a2.35,2.35,0,0,0-.26-1.33.924.924,0,0,0-.85-.37,1.1,1.1,0,0,0-1,.51,3.125,3.125,0,0,0-.3,1.6v3.85h-1.16V56.25c0-.14,0-.3-.01-.48a4.683,4.683,0,0,0-.05-.59h1.18l.06.78a2.269,2.269,0,0,1,.78-.71,2.053,2.053,0,0,1,.98-.22,1.881,1.881,0,0,1,1.32.49,1.784,1.784,0,0,1,.48,1.32v5.04h-1.16Zm2.44-3.35a4.527,4.527,0,0,1,.6-2.66,2.224,2.224,0,0,1,1.92-.82,2.148,2.148,0,0,1,1.9.82,4.6,4.6,0,0,1,.6,2.66,4.76,4.76,0,0,1-.6,2.68,2.184,2.184,0,0,1-1.9.82,2.23,2.23,0,0,1-1.93-.82,4.686,4.686,0,0,1-.59-2.68h0Zm2.52-2.5a1.056,1.056,0,0,0-1.03.59,4.122,4.122,0,0,0-.32,1.92,4.787,4.787,0,0,0,.3,1.97,1.089,1.089,0,0,0,1.04.57,1.058,1.058,0,0,0,1.02-.57,4.436,4.436,0,0,0,.32-1.95,4.377,4.377,0,0,0-.32-1.95,1.068,1.068,0,0,0-1.02-.57h0Zm3.59,3.82h1.25a1.5,1.5,0,0,0,.3.93,1.047,1.047,0,0,0,.82.32,1.153,1.153,0,0,0,.79-.25.766.766,0,0,0,.3-.65.83.83,0,0,0-.22-.54,3.092,3.092,0,0,0-.74-.52l-1.22-.62a2.7,2.7,0,0,1-.92-.7,1.377,1.377,0,0,1-.27-.88,1.721,1.721,0,0,1,.63-1.4,2.538,2.538,0,0,1,1.68-.52,2.407,2.407,0,0,1,1.6.48,1.586,1.586,0,0,1,.6,1.3v.13h-1.24a.986.986,0,0,0-1.08-1.06,1.088,1.088,0,0,0-.73.24.772.772,0,0,0-.29.6.654.654,0,0,0,.16.45,1.573,1.573,0,0,0,.52.36l1.11.56a4.528,4.528,0,0,1,1.3.89,1.687,1.687,0,0,1,.33,1.05,1.824,1.824,0,0,1-.65,1.48,3.011,3.011,0,0,1-3.47,0,2.2,2.2,0,0,1-.59-1.64h0Zm7.47-6.55V55.2h1.33v.84h-1.33v4a1.222,1.222,0,0,0,.16.75.739.739,0,0,0,.6.21c.11,0,.29,0,.52-.02h.05l-.02.86a3.046,3.046,0,0,1-.49.1,3.517,3.517,0,0,1-.45.03,1.676,1.676,0,0,1-1.19-.34,1.649,1.649,0,0,1-.35-1.17V56.05h-1.02v-.84H165V53.88l1.16-.55Zm2.38.22V52.29h1.11v1.24Zm0,8.36V55.2h1.11v6.69Zm6.31-2.25H176a2.427,2.427,0,0,1-.65,1.78,2.381,2.381,0,0,1-1.76.64,2.243,2.243,0,0,1-1.92-.81,4.831,4.831,0,0,1-.59-2.68,4.831,4.831,0,0,1,.59-2.68,2.249,2.249,0,0,1,1.92-.82,2.541,2.541,0,0,1,1.73.57,1.886,1.886,0,0,1,.67,1.51v.14h-1.14a1.307,1.307,0,0,0-.3-.92,1.353,1.353,0,0,0-1.97.24,4.3,4.3,0,0,0-.32,1.97,4.26,4.26,0,0,0,.32,1.95,1.073,1.073,0,0,0,1.03.57,1.089,1.089,0,0,0,.89-.4,1.534,1.534,0,0,0,.36-1.05h0Zm2.16.22h1.25a1.383,1.383,0,0,0,.3.93,1.047,1.047,0,0,0,.82.32,1.17,1.17,0,0,0,.79-.25.766.766,0,0,0,.3-.65.83.83,0,0,0-.22-.54,3.092,3.092,0,0,0-.74-.52l-1.22-.62a2.809,2.809,0,0,1-.92-.7,1.39,1.39,0,0,1-.29-.88,1.709,1.709,0,0,1,.65-1.4,2.557,2.557,0,0,1,1.68-.52,2.407,2.407,0,0,1,1.6.48,1.577,1.577,0,0,1,.59,1.3v.13h-1.22a1.041,1.041,0,0,0-.29-.78,1.076,1.076,0,0,0-.79-.28,1.088,1.088,0,0,0-.73.24.772.772,0,0,0-.29.6.654.654,0,0,0,.16.45,1.473,1.473,0,0,0,.52.36l1.11.56a4.179,4.179,0,0,1,1.29.89,1.586,1.586,0,0,1,.35,1.05,1.8,1.8,0,0,1-.65,1.48,3.012,3.012,0,0,1-3.47,0,2.126,2.126,0,0,1-.59-1.64h0Z"
                                fill="#565355" />
                        </g>
                    </svg>

                </div>
                <!--logo End-->




            </div>
        </a>
        <div class="sidebar-toggle" data-toggle="sidebar" data-active="true">
            <i class="icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.25 12.2744L19.25 12.2744" stroke="currentColor" stroke-width="1.5"
                        stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M10.2998 18.2988L4.2498 12.2748L10.2998 6.24976" stroke="currentColor" stroke-width="1.5"
                        stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </i>
        </div>
    </div>
    <div class="sidebar-body pt-0 data-scrollbar">
        <div class="sidebar-list">
            <!-- Sidebar Menu Start -->


            <ul class="navbar-nav iq-main-menu sidebar-permission placeholder-glow" id="sidebar-menu">
                <!-- Loading starts here -->
                <?php for($i = 0; $i < 15; $i++): ?>
                    <li class="nav-item ps-2 pb-3">
                        <div class="w-100 d-flex gap-3 px-3" data-bs-toggle="collapse" role="button"
                            aria-expanded="false" aria-controls="sidebar-action">
                            <i class="icon placeholder rounded-1 col-1">&nbsp;</i>
                            <span class="item-name placeholder col-10">Action</span>
                        </div>
                    </li>
                <?php endfor; ?>
            </ul>
            <!-- Sidebar Menu End -->
        </div>
    </div>
    <div class="sidebar-footer"></div>
</aside>
<?php /**PATH C:\wamp64\www\mymd-care\resources\views/admin/layouts/sidebar.blade.php ENDPATH**/ ?>