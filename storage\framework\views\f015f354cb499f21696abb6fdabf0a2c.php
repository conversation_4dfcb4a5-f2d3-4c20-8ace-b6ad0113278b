<?php $__env->startSection('title'); ?>
    <?php echo e(config('authorization.title', 'Dashboard')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-12 header-action">
        <div class="row">
            <?php for($i = 0; $i < 6; $i++): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-white db-top-stat-box transition hover-shadow">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-center gap-3 placeholder-glow">
                                <div class=" rounded p-3 flex-shrink-1 placeholder" style="width: 60px; min-height: 60px;">
                                </div>
                                <div class="text-start flex-grow-1">
                                    <h2 class="counter placeholder col-5 mb-2 bg-gray" style="visibility: visible;"></h2>
                                    <span class="placeholder d-block col-4"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endfor; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "<?php echo e(config('authorization.auth_url') . 'dashboardCount'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {};
            setFilter(filter); // set filter [where, pagination, sort]
            getList(); // get list data with pagination and filter
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Authorization\resources/views/admin/dashboard.blade.php ENDPATH**/ ?>