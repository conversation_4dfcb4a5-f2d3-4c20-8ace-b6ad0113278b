@extends('admin.layouts.app')
@section('title')
    {{ config('pharmacy.order_title', 'order') }}
@endsection
@section('meta')
@endsection
@section('style')
@endsection
@section('content')
    <div class="col-sm-12">
        <div class="card  mb-3">
            <div
                class="card-header border-bottom py-2 px-3 px-md-4 align-items-center d-flex justify-content-between rounded-3">
                <div class="header-title">
                    <h5 class="h5 mb-0">Order Medicine</h5>
                </div>
                <div>
                    <a href="{{ route('pharmacy.order.index') }}">
                        <button type="button"
                            class="btn btn-sm btn-primary d-flex gap-1 align-items-center justify-content-center"
                            data-bs-toggle="modal" data-bs-target="#exampleModalCenteredScrollable">
                            <svg fill="none" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24">
                                <path d="M15.5 19L8.5 12L15.5 5" stroke="currentColor" stroke-width="1.5"
                                    stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                            Back
                        </button>
                    </a>
                </div>
            </div>
        </div>
        <div class="card">
            @if (!$id)
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="card mb-3">
                                <div
                                    class="card-header border-bottom py-2 align-items-center d-flex justify-content-between">
                                    <div class="header-title">
                                        <h5 class="h5 mb-0">Search Patient</h5>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row justify-content-center">
                                        <div class="form-group col-lg-6 col-md-6 col-12">
                                            <div class="row row-cols-auto align-items-center justify-content-center">
                                                <label for="exampleInputEmail1"
                                                    class="fw-bold col-sm-12 form-label p-0 text-center">Search with Patient
                                                    Name or Phone No</label>
                                                <div class="col-12 col-sm-12">
                                                    <input type="text" class="form-control" name="search_patient"
                                                        id="search_patient" placeholder="Search">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex gap-1 justify-content-center mb-5">
                                            <a href="{{ route('pharmacy.order.index') }}">
                                                <button type="button" class="btn btn-gray"
                                                    data-bs-dismiss="modal">Close</button>
                                            </a>

                                            <button onclick="searchPatient()" type="button" name="button"
                                                class="btn btn-primary d-flex gap-1 align-items-center justify-content-center">Search</button>
                                        </div>
                                        <div class="col-lg-12 col-md-12 col-12">
                                            <div class="Table-custom-padding1 table-responsive">
                                                <table id="data-list-family"
                                                    class="table table-sm datatable_desc placeholder-glow"
                                                    data-toggle="data-table" style="display: none;">
                                                    <thead>
                                                        <tr>
                                                            <th>
                                                                Name
                                                            </th>
                                                            <th>
                                                                Gender
                                                            </th>
                                                            <th>
                                                                Membership
                                                            </th>
                                                            <th>
                                                                Action
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @include('admin.custom.loading', [
                                                            'td' => 4,
                                                            'action' => 1,
                                                        ])
                                                    </tbody>
                                                    <tfoot>
                                                        <tr>
                                                            <td colspan="5" class="border-0">
                                                                <button
                                                                    class="btn btn-primary d-flex gap-1 align-items-center justify-content-center placeholder"
                                                                    type="button"></button>
                                                            </td>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body placeholder-glow data-add" id="data-add-patient" style="display: none;">
                                @include('admin.custom.loadingForm', ['fields' => [4, 4, 4, 4, 4, 4, 4]])
                            </div>
                            <div class="card-body placeholder-glow data-add" id="data-add-member" style="display: none;">
                                @include('admin.custom.loadingForm', ['fields' => [4, 4, 4, 4, 4]])
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body placeholder-glow" id="data-add-edit" style="display: none">

                </div>
            @else
                <div class="card-header d-flex justify-content-between">
                    <div class="header-title">
                        <h4 class="card-title">Update Order Medicine</h4>
                    </div>
                </div>
                <div class="card-body placeholder-glow" id="data-add-edit">
                    @include('admin.custom.loadingForm', ['fields' => [3, 3, 3, 3]])
                </div>
            @endif
        </div>
    </div>
@endsection
@section('modal')
    <div class="modal fade" id="membershipOtpModel" tabindex="-1" aria-labelledby="exampleModalCenteredScrollableTitle"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Verify Phone Number</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="new-user-info">
                        <div class="alert alert-danger" id="msg_holder1" style="display:none">Please Enter Correct OTP</div>
                        {{-- <h6 class="h6 mt-3 text-primary small text-center" id="msg_holder1" style="display: none;">
                            Please Enter Correct OTP
                        </h6> --}}
                        <div class="row">
                            <div class="col-md-12">
                                <div class="row align-items-center">
                                    <label for="exampleInputEmail1" class="col-12 col-md-3"><span class="h6">Enter
                                            Otp</span></label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control form-control-sm border-gray"
                                            id="otp_for_membership" value="" placeholder=" ">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 mb-4 mt-2">
                                <div class="row justify-content-between">
                                    <div class="col-auto">
                                        <h6 class="d-flex flex-wrap gap-1">
                                            Time Remaining
                                            <span>:</span>
                                            <span class="timerOtpExpire_membership">00:00</span>
                                        </h6>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button"
                                            class="text-primary h6 border-0 p-0 bg-transparent resendOTP_membership"
                                            onclick="resendOTPMembership()" disabled>
                                            Resend OTP
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- end -->
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" id="verifyotp" class="btn btn-primary text-white"
                        onclick="verifyOtpBeforeSubmit(this)">Verify OTP</button>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script>
        @if (!$id)
            $(document).ready(function() {
                refreshToken();
            });
        @endif
        function resendOTPMembership() {
            sendOtpBeforeSubmit();
        }

        function sendOtpBeforeSubmit() {
            $('#otp_for_membership').prop('disabled', false);
            $('.resendOTP_membership').prop('disabled', true);
            if (!$('#clinic_id').val()) {
                swal({
                    title: "",
                    text: "Please select a Pharmacy",
                    type: "warning",
                    confirmButtonColor: '#3085d6',
                    confirmButtonText: 'Ok',
                });
                return false;
            }
            if (!$('#remarks').val()) {
                swal({
                    title: "",
                    text: "Please enter remarks",
                    type: "warning",
                    confirmButtonColor: '#3085d6',
                    confirmButtonText: 'Ok',
                });
                return false;
            }
            $('#msg_holder1').hide();
            $('#membershipOtpModel').modal('show');
            let phone = $('#verify_otp_phone').val();
            $.ajax({
                type: 'POST',
                url: "{{ config('pharmacy.order_url') . 'sendOtpBeforeSubmit' }}",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "phone": phone
                }),
                processData: false,
                success: function(data) {
                    // console.log(data.otp_expire);
                    localStorage.setItem('device_check', data.otp_expire);
                    let durationInSeconds = data.otp_expire * 60;
                    startOTPTimer(durationInSeconds, 'timerOtpExpire_membership', 'resendOTP_membership',
                        'otp_for_membership');
                },
                error: function(response) {
                    // $(params).prop('disabled', false);
                    // $(params).html(btn_text);
                }
            });

        }

        function verifyOtpBeforeSubmit(params) {
            // Retrieve data
            let device_check = localStorage.getItem('device_check');
            let phone = $('#verify_otp_phone').val();
            let otp_for_membership = $('#otp_for_membership').val();
            console.log(device_check);
            if (device_check === null) {
                alert('OTP has been sent to this device. Please verify the OTP on the same device.');
                return false;
            }
            // console.log(otp_user,$('#otp').val());
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "{{ config('pharmacy.order_url') . 'verifyOtpBeforeSubmit' }}",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    "phone": phone,
                    "otp": otp_for_membership
                }),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                    if (data.verify == true) {
                        $('#membershipOtpModel').modal('hide');
                        $('#hiddenFormSubmit').click();
                        // Remove data
                        localStorage.removeItem('device_check');
                    } else {
                        $('#msg_holder1').show();
                    }
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
        // calcutate total_amount
        function calcutaleTotalAmount() {
            let total_amount = 0;
            $('.medicine_ids').each(function() {
                var index_id = $(this).attr('data-id');
                var selectedOption = $(this).find('option:selected');
                var price = Number(selectedOption.data('price'));
                var quantity = Number($('#quantity_' + index_id).val());
                var sub_tot = price * quantity;
                total_amount += sub_tot;
            });
            $('#total_amount').val(total_amount.toFixed(2));
        }

        let latitude;
        let longitude;
        $(function() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(showPosition, showError);
            } else {
                document.getElementById("location-info").innerHTML =
                    "Geolocation is not supported by this browser.";
            }
        });

        function showPosition(position) {
            console.log(position);
            latitude = position.coords.latitude;
            longitude = position.coords.longitude;
            // document.getElementById("location-info").innerHTML = "Latitude: " + latitude +
            // "<br>Longitude: " + longitude;
            // location.reload();
        }

        function showError(error) {
            switch (error.code) {
                case error.PERMISSION_DENIED:
                    document.getElementById("location-info").innerHTML = "User denied the request for Geolocation.";
                    break;
                case error.POSITION_UNAVAILABLE:
                    document.getElementById("location-info").innerHTML = "Location information is unavailable.";
                    break;
                case error.TIMEOUT:
                    document.getElementById("location-info").innerHTML = "The request to get user location timed out.";
                    break;
                case error.UNKNOWN_ERROR:
                    document.getElementById("location-info").innerHTML = "An unknown error occurred.";
                    break;
            }
        }

        function collectionType(params) {
            $('#clinic_show_div').hide();
            $('#delivery_details').hide();
            if (params == 'CV') {
                $('#clinic_show_div').show();
            } else {
                $('#delivery_details').show();
            }
        }
        $(document).ready(function() {
            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });

        function meddivonoff(params) {
            if (params == 1) {
                $('.meddivonoff_div').show();
            } else {
                $('.meddivonoff_div').hide();
            }
        }
        // Event handler for the "+" button
        $(document).on('click', '.button-plus', function() {
            let $input = $(this).siblings('.medquantity');
            let value = parseInt($input.val());
            if (!isNaN(value)) {
                $input.val(value + 1);
                calcutaleTotalAmount();
            }
        });

        // Event handler for the "-" button
        $(document).on('click', '.button-minus', function() {
            let $input = $(this).siblings('.medquantity');
            let value = parseInt($input.val());
            if (!isNaN(value) && value > 1) {
                $input.val(value - 1);
                calcutaleTotalAmount();
            }
        });

        function addMoreMedicine() {
            let rand = getRandomInt(1000, 9999);
            // console.log(rand,'sfuh');
            htmlMedicine(rand);
        }

        function htmlMedicine(rand) {
            let ht_data = '<div class="row" id="medicine_row-' + rand + '" data-select2-id="select2-data-append_lav_div">';
            ht_data += '<div class="col-md-7 col-7 mb-2 prescription_select2" data-select2-id="select2-data-48-ogys">';
            ht_data += '<input type="hidden" id="medicine_price_' + rand + '" value="0">';
            ht_data += '<select id="medicine_id_' + rand +
                '" name="medicine_ids[]" class="select2-multpl-custom1 form-select medicine_ids" data-id="' + rand +
                '" data-style="py-0" onchange="calcutaleTotalAmount()">';
            ht_data += '<option value="">Select</option>';
            @foreach ($list['medicine_list'] as $row)
                ht_data +=
                    "<option value='<?php echo $row['id']; ?>' data-price='<?php echo $row['price']; ?>'><?php echo $row['name']; ?></option>";
            @endforeach
            ht_data += '</select>';
            ht_data += '</div>';
            ht_data += '<div class="col-md-3 col-3 mb-2 mb-md-0 ">';
            ht_data += '<div class="d-flex justify-content-center align-items-center">';
            ht_data +=
                '<input type="button" value="-" class="button_minus button-minus border rounded-circle  icon-shape icon-sm mx-1 " data-field="quantity" style="width: 25px; height:25px;">';
            ht_data +=
                '<input type="number" class="col-md-3 form-control form-control-sm text-center pe-md-0 medquantity" name="quantitys[]" id="quantity_' +
                rand +
                '" value="1" min="1" style="width: 50px;" placeholder="" readonly="" onchange="calcutaleTotalAmount()">';
            ht_data +=
                '<input type="button" value="+" class="button_plus button-plus border rounded-circle icon-shape icon-sm mx-1 " data-field="quantity" style="width: 25px; height:25px;">';
            ht_data += '</div>';
            ht_data += '</div>';
            ht_data += '<div class="col-md-2 col-2 d-flex justify-content-center align-items-center mb-2 mb-md-0">';
            ht_data += '<button type="button" title="Remove" onclick="removeMedicine(' + rand +
                ')" class="btn btn-sm bg-transparent border-0 p-0">';
            ht_data +=
                '<svg height="12pt" id="fi_1214428" viewBox="-40 0 427 427.00131" width="12pt" xmlns="http://www.w3.org/2000/svg">';
            ht_data +=
                '<path d="m232.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0"></path><path d="m114.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0"></path><path d="m28.398438 127.121094v246.378906c0 14.5625 5.339843 28.238281 14.667968 38.050781 9.285156 9.839844 22.207032 15.425781 35.730469 15.449219h189.203125c13.527344-.023438 26.449219-5.609375 35.730469-15.449219 9.328125-9.8125 14.667969-23.488281 14.667969-38.050781v-246.378906c18.542968-4.921875 30.558593-22.835938 28.078124-41.863282-2.484374-19.023437-18.691406-33.253906-37.878906-33.257812h-51.199218v-12.5c.058593-10.511719-4.097657-20.605469-11.539063-28.03125-7.441406-7.421875-17.550781-11.5546875-28.0625-11.46875h-88.796875c-10.511719-.0859375-20.621094 4.046875-28.0625 11.46875-7.441406 7.425781-11.597656 17.519531-11.539062 28.03125v12.5h-51.199219c-19.1875.003906-35.394531 14.234375-37.878907 33.257812-2.480468 19.027344 9.535157 36.941407 28.078126 41.863282zm239.601562 279.878906h-189.203125c-17.097656 0-30.398437-14.6875-30.398437-33.5v-245.5h250v245.5c0 18.8125-13.300782 33.5-30.398438 33.5zm-158.601562-367.5c-.066407-5.207031 1.980468-10.21875 5.675781-13.894531 3.691406-3.675781 8.714843-5.695313 13.925781-5.605469h88.796875c5.210937-.089844 10.234375 1.929688 13.925781 5.605469 3.695313 3.671875 5.742188 8.6875 5.675782 13.894531v12.5h-128zm-71.199219 32.5h270.398437c9.941406 0 18 8.058594 18 18s-8.058594 18-18 18h-270.398437c-9.941407 0-18-8.058594-18-18s8.058593-18 18-18zm0 0"></path><path d="m173.398438 154.703125c-5.523438 0-10 4.476563-10 10v189c0 5.519531 4.476562 10 10 10 5.523437 0 10-4.480469 10-10v-189c0-5.523437-4.476563-10-10-10zm0 0"></path>';
            ht_data += '</svg>';
            ht_data += '</button>';
            ht_data += '</div>';
            ht_data += '</div>';
            $('#medicine_box').append(ht_data);
            $('.select2-multpl-custom1').select2();
        }

        function removeMedicine(id) {
            // console.log(id);
            $('#medicine_row-' + id).remove();
            calcutaleTotalAmount();
        }

        function searchPatient() {
            let phone = $('#search_patient').val();
            if (phone == '') {
                alert('Please enter phone number');
                return false;
            }
            const validationErrors = validatePhoneNumber(phone);
            if (Object.keys(validationErrors).length > 0) {
                alert(validationErrors.phone);
                return false;
            }
            $('#data-add-edit').html('');
            $('.data-add').hide();
            let datajson = {
                "phone": phone
            };
            $.ajax({
                type: "POST",
                url: "{{ config('pharmacy.order_url') . 'listFamily' }}",
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(datajson), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                beforeSend: function() {
                    // Show the loading GIF
                    $('#data-list-family').show();
                },
                success: function(data) {
                    $('#data-list-family tbody').html(data.tbody);
                    $('#data-list-family tfoot').html(data.tfoot);
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                },
                complete: function() {
                    // Hide the loading GIF
                    // $('#loadingList').hide();
                }
            });
        }

        function showForm(stat, parent_id, phone) {
            $('.data-add').hide();
            let redirectUrl = "{{ route('pharmacy.order.addForm') }}";
            setRedirectUrl(redirectUrl);
            if (stat == 0) {
                let ht_id = '#data-add-member';
                setId(ht_id); // set show table id
                let url = "{{ config('patient.url') . 'addFamily/' }}" + parent_id;
                setListUrl(url); // api url for show table
            } else {
                let ht_id = '#data-add-patient';
                setId(ht_id); // set show table id
                let url = "{{ config('patient.url') . 'create' }}" + "?parent_id=" + parent_id + "&phone=" + phone;
                setListUrl(url); // api url for show table
            }
            $(ht_id).show();
            let method = 'GET';
            setMethod(method);
            let filter = {};
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter
        }

        function addOrder(patient_id, phone, service) {
            // console.log(latitude,longitude);
            // return false;
            $('.data-add').hide();
            let clinic_id = "{{ request('clinic_id') }}";
            let task_id = "{{ request('task_id') }}";
            let source_id = "{{ request('source_id') }}";
            let redirectUrl = "{{ route('pharmacy.order.index') }}?clinic_id=" + clinic_id + "&task_id=" + task_id +
                "&source_id=" + source_id;
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-add-edit';
            $(ht_id).show();
            setId(ht_id); // set show table id
            let url = "{{ $id ? config('pharmacy.order_url') . 'edit/' . $id : config('pharmacy.order_url') . 'create' }}";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let filter = {
                "patient_id": patient_id,
                "phone": phone,
                "service": service,
                "latitude": latitude,
                "longitude": longitude,
                "agent_clinic_id": "{{ request('clinic_id') }}",
                "agent_source_id": "{{ request('source_id') }}"
            };
            setFilter(filter); // set filter [where, pagination, sort]
            getForm(); // get list data with pagination and filter
        }
        @if ($id)
            addOrder(0, 0);
        @endif
    </script>
    @if (null !== request('pid'))
        <script>
            var pid = "{{ request('pid') }}";
            var phone = "{{ request('phone') }}";
            var service = "{{ request('service') }}";
            $('#search_patient').val(phone);
            searchPatient();
            addOrder(pid, phone, service);
            const targetElement = document.getElementById("data-add-edit");
            targetElement.scrollIntoView({
                behavior: "smooth",
                block: "center"
            });
        </script>
    @endif
@endpush
