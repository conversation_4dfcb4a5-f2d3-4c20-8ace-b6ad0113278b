<?php

namespace Modules\Pharmacy\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Pharmacy\Http\Requests\PharmacyRequest;
use Modules\Pharmacy\Http\Requests\OrderAcceptRequest;
use Modules\Pharmacy\Http\Requests\OrderRejectRequest;
use Modules\Pharmacy\Services\PharmacyService;
use Modules\Pharmacy\Services\PharmacyChildService;
use Modules\Reward\Services\RewardService;
use App\Services\SMSMsgService;
use App\Services\ImageUploadService;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Modules\Reward\Models\VerifyOtp;
use Validator;
use DB;
use PDF;

class PharmacyController extends Controller
{
    private $pharmacyService;
    private $pharmacyChildService;
    private $smsMsgService;
    private $imageUploadService;
    private $rewardService;
    private $otpExpire;//minutes

    public function __construct(PharmacyService $pharmacyService, PharmacyChildService $pharmacyChildService, SMSMsgService $smsMsgService, ImageUploadService $imageUploadService, RewardService $rewardService)
    {
        $this->pharmacyService = $pharmacyService;
        $this->pharmacyChildService = $pharmacyChildService;
        $this->smsMsgService = $smsMsgService;
        $this->imageUploadService = $imageUploadService;
        $this->rewardService = $rewardService;
        $this->otpExpire = env('OTP_EXPIRE_MINUTES');//minutes
    }
    public function index(Request $request)
    {
        $data = [
            'accept_status_list' => Arr::except(config('pharmacy.status_list'), [1,2,3,4,5,6,10]),
            'reject_status_list' => [
                1=>'Call not connected',
                2=>'Patient refused',
                3=>'Out of stock',
                4=>'Other',
            ],
            'data_source_list' => config('pharmacy.data_source_list')
        ];
        // dd($data);
        return view('pharmacy::order.index',compact('data'));
    }
    public function addForm(Request $request)
    {
        $id = $request->id ? $request->id : null;
        $list = [
            'medicine_list' => $this->pharmacyService->allMedicines()->toArray()
        ];
        return view('pharmacy::order.add',compact('id','list'));
    }
    public function list(Request $request)
    {
        try {
            $request['join'] = [
                'patients' => [
                    'reletion' => [
                        'prefix' => 'patient_id',
                        'suffix' => 'id'
                    ]
                ],
                'clinics' => [
                    'reletion' => [
                        'prefix' => 'clinic_id',
                        'suffix' => 'id'
                    ]
                ]
            ];
            $request['with'] = [
                'orderChildMedicines' => 'id,order_id,medicine_id,quantity',
                'orderChildMedicines.medicine' => 'id,name',
            ];
            $filter = $request['filter'];
            $role = $this->getUserRole();
            if ($role->id == 9) {
                $filter['created_by'] =  [
                    'type' => 'eq',
                    'value' => $this->getUserId()
                ];
            }
            
            $request->merge([
                'filter' => $filter
            ]);
            array_push($this->pharmacyService->columns,'patients.name as patient_name','clinics.clinic_name as clinic_name');
            $this->pharmacyService->setRequest($request);
            $this->pharmacyService->findAll();
            $this->response['success']  = true;
            $data = $this->pharmacyService->getRows();
            $status_list = Arr::except(config('pharmacy.status_list'), []);
            // dd($data);
            $permissionPage = $this->getPermissionList();
            // dd($status_list);
            $this->response['tbody'] = view('pharmacy::order.api.list',compact('data','permissionPage','status_list'))->render();
            $this->response['tfoot'] = $this->pharmacyService->paginationCustom();
            $this->response['headerAction'] = view('pharmacy::order.api.headerAction',compact('permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }

    public function listFamily(Request $request)
    {
        $this->response['success']  = true;
        $phone = $request->phone;
        $this->response['data'] = [];
        $list = [
            'patients' => $this->pharmacyService->getFamilys($phone)
        ];
        // dd($list);
        $parent_id = $this->pharmacyService->getPatientID($phone);
        if (!isset($parent_id)) {
            $parent_id = 0;
        }
        // dd($phone,$parent_id,$list);
        $html = '<tr>';
            $html .= '<td colspan="5" class="border-0">';
            if (count($list['patients']) > 0) {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(0,'.$parent_id.','.$request->phone.')">Add Member</button>';
            }
            else {
                $html .= '<button class="btn btn-primary d-flex gap-1 align-items-center justify-content-center" type="button" onclick="showForm(1,'.$parent_id.','.$request->phone.')">Add Patient</button>';
            }
            $html .= '</td>';
        $html .= '</tr>';
        // dd($list['patients'],$list['patients'][0]->membershipRegistrations[0]->memberships);
        $this->response['tbody'] = view('pharmacy::order.api.listFamily',compact('list','parent_id','phone'))->render();
        $this->response['tfoot'] = $html;
        return response()->json($this->response);
    }
    public function create(Request $request)
    {
        // dd($request->appointment_type);
        try {
            $id = null;
            $patient_id = $request->patient_id;
            $phone = $request->phone;
            $service = $request->service;
            $agent_clinic_id = $request->agent_clinic_id;
            $agent_source_id = $request->agent_source_id;
            // $appointment_type = $request->appointment_type;
            $this->response['success']  = true;
            $patient = $this->pharmacyService->findPatient($patient_id);

            // dd($otc_check);
            $data = [
                'patient_detail' => $patient,
                'phone' => $phone,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'agent_clinic_id' => $agent_clinic_id ?? null,
                'agent_source_id' => $agent_source_id ?? null
            ];

            $list = [
                'clinic_list' => $this->pharmacyService->allClinics()->toArray(),
                'medicine_list' => $this->pharmacyService->allMedicines()->toArray()
            ];
            // if ($appointment_type == 2) {
            //     $list['tc_clinic_list'] = $this->pharmacyService->allClinics();
            // }
            // dd($list);
            $permissionPage = $this->getPermissionList();
            $this->response['form'] = view('pharmacy::order.api.addEdit',compact('id','patient_id','phone','data','list','service','permissionPage'))->render();
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function sendOtpBeforeSubmit(Request $request)
    {
        try {
            $check = $this->rewardService->findByOtp($request->phone, 'MB-Reg');
            $created_at = date('Y-m-d H:i:s');
            $validated_at = date('Y-m-d H:i:s', strtotime('+'.$this->otpExpire.' minutes'));
            $otp = rand(1000, 9999);
            $data = [
                'phone' => $request->phone,
                'type' => 'Pharmacy-Order',
                'otp' => $otp,
                'created_by' => $this->createdBy(),
                'created_at' => $created_at,
                'validated_at' => $validated_at,
                'status' => 0
            ];
            // dd($check);
            $this->rewardService->updateOtp($data);

            $this->smsMsgService->setFlowId('656edb27d6fc050f1e70af33');
            $this->smsMsgService->sendSmsToRecipients($request->phone);
            $variable = [
                'OTP' => $otp
            ];
            $this->smsMsgService->setVariable($variable);
            $data = $this->smsMsgService->send();
            $req_data = [
                'created_by' => $this->createdBy(),
                'event' => 'Pharmacy Order OTP',
                'campaign_name' => 'Sent OTP for Pharmacy Order',
                'status' => 2,
                'reason' => ''
            ];
            $this->smsMsgService->add($req_data);

            $this->response['success']  = true;
            $this->response['otp_expire']     = $this->otpExpire;
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function verifyOtpBeforeSubmit(Request $request)
    {
        try {
            // dd($request->all());
            $checkOtp = $this->rewardService->checkOtp($request->phone, 'Pharmacy-Order');
            if ($checkOtp['otp'] == $request->otp) {
                VerifyOtp::where('id',$checkOtp['id'])->update([
                    'status' => 1
                ]);
                $this->response['verify']  = true;
            }
            else {
                $this->response['verify']  = false;
            }
            $this->response['success']  = true;
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function add(PharmacyRequest $request)
    {
        // dd($request,$request->file('files'));
        try {
            $request->validated();
            $role = $this->getUserRole();
            $request->merge([
                'created_by' => $this->createdBy(),
                'type_of_collection' => 'CV',
                'source' => $request->agent_source_id,
                'status' => ($role->id == 9 ? 1 : 5)
            ]);
            // dd($request->all());
            if ($request->hasFile('files')) {
                $fileinfo = $request->file('files');
                $this->imageUploadService->setFieldName('files');
                $this->imageUploadService->setFilePath('orderMedicine/');
                $data = $this->imageUploadService->uploadOneFile();
                $response =$this->imageUploadService->getUrls();
                $request->merge([
                    'prescription_upload' => $response[0]['name']
                ]);
            }
            $this->pharmacyService->setRequest($request);
            $order = $this->pharmacyService->add();
            if (isset($request->medicine_ids)) {
                foreach ($request->medicine_ids as $key => $row) {
                    $request['order_id'] = $order->id;
                    $request['medicine_id'] = $row;
                    $request['quantity'] = $request->quantitys[$key];
                    $this->pharmacyChildService->setRequest($request->except('status','medicine_ids','quantitys'));
                    $this->pharmacyChildService->add();
                }
            }
            $this->response['success']  = true;
            $this->response['message']  = 'Order Request Submitted Successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
    public function edit($id,Request $request)
    {
        try {
            $service = $request->service;
            // $appointment_type = $request->appointment_type;
            $this->response['success']  = true;
            $order = $this->pharmacyService->findById($id);
            // dd($order->patient);
            if (!$order) {
                $this->response['success']    = false;
                $this->response['message']  = 'order not found!';
                return response()->json($this->response);
            }
            $this->response['success']  = true;
            $data = [
                'order_detail' => $order,
                'patient_detail' => $order->patient,
                'phone' => $order->patient_phone,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'status_list' => Arr::except(config('pharmacy.status_list'), [])
            ];

            $list = [
                'clinic_list' => $this->pharmacyService->allClinics()->toArray(),
                'medicine_list' => $this->pharmacyService->allMedicines()->toArray()
            ];
            $this->response['form'] = view('pharmacy::order.api.addEdit',compact('id','data','list'))->render();
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function update($id,PharmacyRequest $request)
    {
        // dd($id);
        try {
            $request->validated();

            $order = $this->pharmacyService->findById($id);

            if (!$order) {
                $this->response['success']  = false;
                $this->response['message']  = 'order not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            // dd($request->all());
            $this->pharmacyService->setRequest($request);
            $this->pharmacyService->update();
            DB::table('order_child_medicines')->where('order_id', $id)->delete();
            if (isset($request->medicine_ids)) {
                foreach ($request->medicine_ids as $key => $row) {
                    $request['order_id'] = $id;
                    $request['medicine_id'] = $row;
                    $request['quantity'] = $request->quantitys[$key];
                    $request['created_by'] = $this->createdBy();
                    $this->pharmacyChildService->setRequest($request->except('status','medicine_ids','quantitys','modified_by'));
                    $this->pharmacyChildService->add();
                }
            }
            $this->response['success']  = true;
            $this->response['message']  = 'Order has been updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }

    public function updateStatus($id,Request $request)
    {
        // dd($request->all());
        try {
            $order = $this->pharmacyService->findById($id);
            if (!$order) {
                $this->response['success']  = false;
                $this->response['message']  = 'Order not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $this->pharmacyService->setRequest($request);
            $this->pharmacyService->update();

            $this->response['success']  = true;
            $this->response['message']  = 'Order medicine has been status updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function acceptOrder(OrderAcceptRequest $request)
    {
        try {
            $order = $this->pharmacyService->findById($request->order_id);
            if (!$order) {
                $this->response['success']  = false;
                $this->response['message']  = 'Order not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'is_accept' => 1
            ]);
            if($request->type_of_collection == 'CV'){
                $request->merge([
                    'full_address' => null,
                    'landmark' => null,
                    'city' => null,
                    'pincode' => null
                ]);
            }
            
            $this->pharmacyService->setRequest($request);
            $this->pharmacyService->update();

            $this->response['success']  = true;
            $this->response['message']  = 'Order medicine has been status updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function rejectOrder(OrderRejectRequest $request)
    {
        try {
            $order = $this->pharmacyService->findById($request->order_id);
            if (!$order) {
                $this->response['success']  = false;
                $this->response['message']  = 'Order not found!';
                return response()->json($this->response);
            }
            $request->merge([
                'modified_by' => $this->modifiedBy(),
                'status' => 6,
                'is_accept' => 2,
                'reject_remarks' => ($request->reject_status == 'Other' ? $request->reject_remarks : $request->reject_status)
            ]);
            
            $this->pharmacyService->setRequest($request);
            $this->pharmacyService->update();

            $this->response['success']  = true;
            $this->response['message']  = 'Order medicine has been status updated successfully!';
            $this->response['data']     = [];
            return response()->json($this->response);

        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
             return response()->json($this->response);
        }
    }
    public function prescriptionUpload(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required',
                'files' => 'required|file|mimes:jpeg,png,jpg,gif,svg,pdf|max:2048',
            ],
            [
                'order_id.required' => 'Order id is required.',
                'files.required' => 'Prescription is required.',
                'files.file' => 'Prescription must be a file.',
                'files.mimes' => 'Prescription must be a file of type: jpeg, png, jpg, gif, svg, pdf.',
                'files.max' => 'Prescription may not be greater than 2mb.',
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }
            $order = $this->pharmacyService->findById($request->order_id);
            // dd($request->all(),$order);
            if (!$order) {
                $this->response['success']  = false;
                $this->response['message']  = 'Order not found!';
                return response()->json($this->response);
            }
            // dd($request->all());
            if ($request->hasFile('files')) {
                $fileinfo = $request->file('files');
                $this->imageUploadService->setFieldName('files');
                $this->imageUploadService->setFilePath('orderMedicine/');
                $data = $this->imageUploadService->uploadOneFile();
                $response =$this->imageUploadService->getUrls();
                $request->merge([
                    'prescription_upload' => $response[0]['name']
                ]);
            }
            // dd($prescription_upload);
            $request->merge([
                'modified_by' => $this->modifiedBy()
            ]);
            $this->pharmacyService->setRequest($request);
            $this->pharmacyService = $this->pharmacyService->update();
            $this->response['success']  = true;
            $this->response['res_data'] = 'uploaded prescription';
            $this->response['message']  = 'Prescription has been uploaded successfully!';
            return response()->json($this->response);
        } catch (\Exception $e) {
            $this->response['success']  = false;
            $this->response['message']  = $e->getMessage();
            return response()->json($this->response);
        }
    }
}
