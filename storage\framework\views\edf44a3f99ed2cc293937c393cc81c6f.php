<?php $__env->startSection('title'); ?>
    <?php echo e(config('schedule.title', 'Schedule')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('meta'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('style'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex flex-wrap gap-3 justify-content-between p-3 pb-0 p-md-4 pb-md-0">
                <div class="header-title">
                    <h4 class="card-title">Schedules</h4>
                </div>

                <div class="d-flex flex-wrap gap-2 header-action">

                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="row">
                            <div class="col">
                                <label class="mb-1 d-flex gap-2 align-items-center">
                                    <span>Show</span>
                                    <select id="perPageCount" class="form-select form-select-sm px-2" style="max-width: 70px;">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                    <span>entries</span>
                                </label>
                            </div>
                            <div class="col">
                                <button type="button" class="btn btn-primary btn-sm" onclick="exportCSV(this)">CSV</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8 mb-3 mb-md-0">
                        <div class="row justify-content-end gx-md-2 gy-2">
                            <div class="col-md-4">
                                <select name="clinic_id" class="select2-multpl-custom1 form-select search-change"
                                    data-style="py-0">
                                    <option value="">Filter By clinic</option>
                                    <?php $__currentLoopData = $data['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($row['id']); ?>"><?php echo e($row['clinic_name']); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" name="date" id="date_range"
                                    placeholder="Please select a date range"
                                    class="form-control form-control-sm flatpickr-input active search-date-range"
                                    readonly="readonly">
                            </div>
                            <div class="col-md-4">
                                <input type="search" class="form-control form-control-sm search" placeholder="Search"
                                    data-index="0,1">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="Table-custom-padding1 table-responsive">
                    <table id="data-list" class="table table-sm datatable_desc placeholder-glow" data-toggle="data-table">
                        <thead>
                            <tr>
                                <th>
                                    Id
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="0" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="0" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Doctor
                                </th>

                                <th>
                                    Doctor Type
                                </th>
                                <th>
                                    Clinic
                                </th>
                                <th>
                                    Date
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="1" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="1" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Scheduled Time
                                </th>
                                <th>
                                    Time In
                                </th>
                                <th>
                                    Time Out
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php echo $__env->make('admin.custom.loading', ['td' => 9, 'action' => 1], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tbody>
                        <tfoot>
                            <?php echo $__env->make('admin.custom.loadingPagination', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('modal'); ?>
    <div class="modal fade" id="exampleInfoModal" tabindex="-1" aria-labelledby="exampleModalCenteredScrollableTitle"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content" id="info-div-modal">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Schedule Info</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $("#date_range").flatpickr({
            mode: "range",
            //   maxDate: "today"
        });
        $(document).ready(function() {
            let redirectUrl = "<?php echo e(route('schedule.index')); ?>";
            setRedirectUrl(redirectUrl);
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "<?php echo e(config('schedule.url') . 'list'); ?>";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [
                "id",
                "date"
            ];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {
                    "date": {
                        "type": "eq",
                        "value": "<?php echo e(date('Y-m-d')); ?>"
                    }
                },
                // "collect": {
                //     "clinics": {
                //         "table": "clinics",
                //         "condition": "id",
                //         "jsonValue": "clinic_id",
                //         "select": "clinic_name"
                //     },
                //     "users": {
                //         "table": "users",
                //         "condition": "id",
                //         "jsonValue": "doctor_id",
                //         "select": "id,username"
                //     },
                //     "doctors": {
                //         "table": "doctors",
                //         "condition": "user_id",
                //         "jsonValue": {
                //             "sub_query": "users",
                //             "field": "id"
                //         },
                //         "select": "doctor_type"
                //     },
                //     "doctor_types": {
                //         "table": "doctor_types",
                //         "condition": "id",
                //         "jsonValue": {
                //             "sub_query": "doctors",
                //             "field": "doctor_type"
                //         },
                //         "select": "title"
                //     }
                // },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 3,
                    "offset": 0
                },
                "sort": {
                    "date": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });

        function updateTimeInOut(url, method, parent) {
            // console.log(url,method);
            var parentForm = $(parent);
            if (!jwtToken) {
                redirectToSwal(loginUrl);
                return false;
            }
            let is_time = $(parent).attr('data-stat');
            let changeis_time = (is_time == 2 ? 1 : 3);
            $(parent).attr('data-stat', changeis_time);
            $.ajax({
                type: method,
                url: url,
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    is_time: is_time
                }), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                beforeSend: function () {
                    parentForm.prop('disabled', true);
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    parentForm.html(ht_data);
                },
                success: function(data) {
                    // console.log(data.data,data.is_time,data.curTime);
                    parentForm.prop('disabled', false);
                    if (data.data.is_time == 1) {
                        $('#time_in'+data.data.id).text(data.data.curTime);
                        parentForm.html('Time Out');
                    }
                    else{
                        $('#time_out'+data.data.id).text(data.data.curTime);
                        parentForm.remove();
                    }
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                }
            });
        }
        function exportCSV(params) {
            var btn_text = $(params).html();
            $.ajax({
                type: 'POST',
                url: "<?php echo e(config('schedule.url') . 'export'); ?>",
                crossDomain: true,
                dataType: 'json',
                cache: false,
                contentType: "application/json", // Correct header for sending JSON data
                // contentType: false,
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify(filter),
                processData: false,
                beforeSend: function() {
                    $(params).prop('disabled', true);
                    btn_text = $(params).html();
                    var ht_data =
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                    $(params).html(ht_data);
                },
                success: function(data) {
                    // Store data
                    console.log(data);
                    window.location.href = data.url;
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                },
                error: function(response) {
                    $(params).prop('disabled', false);
                    $(params).html(btn_text);
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\mymd-care\Modules/Schedule\resources/views/schedule/index.blade.php ENDPATH**/ ?>