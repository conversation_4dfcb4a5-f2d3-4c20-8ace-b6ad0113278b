@extends('admin.layouts.app')
@section('title')
    {{ config('pharmacy.order_title', 'Order Medicine') }}
@endsection
@section('meta')
@endsection
@section('style')
@endsection
@section('content')
    <div class="col-sm-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div class="header-title">
                    <h4 class="card-title">Order Medicines</h4>
                </div>

                <div class="header-action">

                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <label class="mb-1 d-flex gap-2 align-items-center">
                            <span>Show</span>
                            <select id="perPageCount" class="form-select form-select-sm px-1">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span>entries</span>
                        </label>
                    </div>
                    <div class="col-md-10 ps-md-5">
                        <div class="row justify-content-end">
                            <div class="col-md-4">
                                <select name="data_source" class="select2-multpl-custom1 form-select search-change"
                                    data-style="py-0">
                                    <option value="">Filter By Data Source</option>
                                    @foreach ($data['data_source_list'] as $key => $row)
                                        <option value="{{ $key }}">{{ $row }}</option>
                                    @endforeach
                                </select>
                            </div>
                            {{-- <div class="col-md col-6 px-1 mb-1 mb-md-0" style="max-width: 230px;">
                                <input type="search" class="form-control form-control-sm search" placeholder="Search"
                                    data-index="0,1,2,3">

                            </div> --}}
                        </div>
                    </div>
                </div>

                <div class="Table-custom-padding1 table-responsive">
                    <table id="data-list" class="table table-sm datatable_desc placeholder-glow" data-toggle="data-table">
                        <thead>
                            <tr>
                                <th>
                                    ID
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="0" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="0" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Patient Name
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="1" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="1" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Phone
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="2" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="2" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Medicine &nbsp; &nbsp; Qty
                                </th>
                                <th>
                                    Pharmacy
                                    <span class="asc-dsc float-end">
                                        <button type="button" data-index="3" data-sort="asc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-up-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="m7.247 4.86-4.796 5.481c-.566.647-.106 1.659.753 1.659h9.592a1 1 0 0 0 .753-1.659l-4.796-5.48a1 1 0 0 0-1.506 0z" />
                                            </svg></button>
                                        <button type="button" data-index="3" data-sort="desc" class="sort"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                fill="currentColor" class="bi bi-caret-down-fill" viewBox="0 0 16 16">
                                                <path
                                                    d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z" />
                                            </svg></button>
                                    </span>
                                </th>
                                <th>
                                    Collection Type
                                </th>
                                <th>
                                    Total Amount
                                </th>
                                <th>
                                    Data Source
                                </th>
                                <th>
                                    Status
                                </th>
                                <th>
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @include('admin.custom.loading', ['td' => 10, 'action' => 1])
                        </tbody>
                        <tfoot>
                            @include('admin.custom.loadingPagination')
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('modal')
    <div class="modal fade" id="exampleInfoModal" tabindex="-1" aria-labelledby="exampleModalCenteredScrollableTitle"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content" id="info-div-modal">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Appointment Info</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="medicineAcceptModal" tabindex="-1"
        aria-labelledby="exampleModalCenteredScrollableTitle" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered ">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Medicine Order Status
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="new-user-info">
                        <form class="clearfix" method="post" action="{{ config('pharmacy.order_url') . 'acceptOrder' }}"
                            data-mode="add" enctype="multipart/form-data" id="submitFormAccept">
                            <div class="row">
                                <input type="hidden" name="order_id" id="accept_order_id">
                                <div class="form-group col-md-6 panel">
                                    <label for="exampleInputClinic" class="form-label fw-bold">Select
                                        Status</label>
                                    <select class="form-select form-select-sm m-bot15 px-2 pe-4" name="status"
                                        id="status">
                                        @foreach ($data['accept_status_list'] as $key => $item)
                                            <option value="{{ $key }}">{{ $item }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div>
                                    <div class="d-flex gap-2">
                                        <div class="form-check px-0">
                                            <input type="radio" class="btn-check" autocomplete="off" value="CV"
                                                onclick="getDeleiveryInfo('CV');" name="type_of_collection"
                                                id="type_of_collection_cv" checked="checked">
                                            <label class="btn btn-sm btn-outline-primary" for="type_of_collection_cv">Store
                                                Pickup</label>
                                        </div>
                                        <div class="form-check px-0">
                                            <input type="radio" class="btn-check" autocomplete="off" value="HC"
                                                onclick="getDeleiveryInfo('HC');" name="type_of_collection"
                                                id="type_of_collection_hc">
                                            <label class="btn btn-sm btn-outline-primary" for="type_of_collection_hc">Home
                                                Delivery</label>
                                        </div>
                                    </div>
                                    <div id="type_of_collectionDiv" style="display: none">
                                        <div class="row mt-4">
                                            <div class="form-group col-md-8">
                                                <label for="exampleInputAddress" class="form-label fw-bold">Full Address
                                                    *</label>
                                                <input type="text" class="form-control form-control-sm"
                                                    name="full_address" id="full_address" value=""
                                                    placeholder="Address">
                                            </div>
                                            <div class="form-group col-md-4">
                                                <label for="exampleInputLandmark" class="form-label fw-bold">Landmark
                                                    *</label>
                                                <input type="text" class="form-control form-control-sm"
                                                    name="landmark" id="landmark" value=""
                                                    placeholder="Landmark">
                                            </div>
                                            <div class="form-group col-md-4">
                                                <label for="exampleInputCity" class="form-label fw-bold">City *</label>
                                                <input type="text" class="form-control form-control-sm" name="city"
                                                    id="city" value="" placeholder="City">
                                            </div>
                                            <div class="form-group col-md-4">
                                                <label for="exampleInputPincode"
                                                    class="form-label fw-bold numfrmt">Pincode *</label>
                                                <input type="text" class="form-control form-control-sm" maxlength="10"
                                                    name="pincode" id="pincode" value="" placeholder="Pincode">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer border-0">
                                <button type="button" class="btn btn-gray" data-bs-dismiss="modal">Close</button>
                                <button type="submit" name="submit" class="btn btn-primary text-white">Submit</button>
                            </div>
                            <div class="form-group col-md-12">
                                <div id="errorMessageAccept" class="" style="color: red;"></div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="medicineRejectModal" tabindex="-1"
        aria-labelledby="exampleModalCenteredScrollableTitle" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered ">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Rejection Reason
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="new-user-info">
                        <form class="clearfix" method="post" action="{{ config('pharmacy.order_url') . 'rejectOrder' }}"
                            data-mode="add" enctype="multipart/form-data" id="submitFormReject">
                            <div class="row">
                                <input type="hidden" name="order_id" id="reject_order_id">
                                <div class="form-group col-md-6 panel">
                                    <label for="exampleInputClinic" class="form-label fw-bold">Select
                                        Status</label>
                                    <select class="form-select form-select-sm m-bot15 px-2 pe-4" name="reject_status"
                                        id="reject_status" onclick="getRejectReason(this.value)">
                                        @foreach ($data['reject_status_list'] as $key => $item)
                                            <option value="{{ $item }}">{{ $item }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div id="reject_remarksDiv" style="display: none">
                                    <div class="row mt-4">
                                        <div class="form-group col-md-8">
                                            <label for="exampleInputAddress" class="form-label fw-bold">Reason
                                                *</label>
                                            <input type="text" class="form-control form-control-sm"
                                                name="reject_remarks" id="reject_remarks" value=""
                                                placeholder="Explain Rejection Reason">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer border-0">
                                <button type="button" class="btn btn-gray" data-bs-dismiss="modal">Close</button>
                                <button type="submit" name="submit" class="btn btn-primary text-white">Submit</button>
                            </div>
                            <div class="form-group col-md-12">
                                <div id="errorMessageReject" class="" style="color: red;"></div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="exampleModalCenter" tabindex="-1" aria-labelledby="exampleModalCenteredScrollableTitle"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content" id="info-div-modal">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exampleModalCenteredScrollableTitle">Upload Prescription</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form class="clearfix" method="post"
                        action="{{ config('pharmacy.order_url') . 'prescriptionUpload' }}"
                        data-mode="add" enctype="multipart/form-data" id="submitForm">
                        <input type="hidden" name="order_id" id="order_id" value="text">
                        <div class="row">
                            <div class="form-group col-md-12">
                                <label class="form-label fw-bold" for="cname">Upload Prescription</label>
                                <div class=" input-group input-group-sm d-flex fs-4">
                                    <input type="file" class="form-control form-control-sm bg-light text-gray h7"
                                        name="files" value=""
                                        onchange="previewImage(this, 'previewImage')">
                                    <div id="previewImage"
                                        class="d-inline-flex align-items-center justify-content-center bg-light">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                aria-label="Close">Close</button>
                            <button type="submit" name="submit" class="btn btn-primary">Save changes</button>
                            <div class="form-group col-md-12">
                                <div id="errorMessage" class="" style="color: red;"></div>
                            </div>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
@endsection
@push('scripts')
    <script>
        function uploadPrescription(id) {
            $('#order_id').val(id);
            $('#exampleModalCenter').modal('show');
        }
        $(document).ready(function() {
            $(document).on("submit", "#submitForm", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var form = $(this).closest('form');
                setFormEntity(form);
                addUpdateForm();
            });
        });
        function resChildHtml(data) {
            $('#exampleModalCenter').modal('hide');
            getList();
        }
        $(document).ready(function() {
            $(document).on("submit", "#submitFormAccept", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var formEntity = $(this).closest('form');
                var formData = new FormData($(formEntity)[0]);
                if (appendField && Object.keys(appendField).length !== 0) {
                    Object.entries(appendField).forEach(([key, value]) => {
                        formData.append(key, value);
                    });
                }
                var parentForm = $(formEntity).closest('form');
                action = $(formEntity).attr("action");
                method = $(formEntity).attr("method");
                var mode = $(formEntity).attr('data-mode');
                var btn_text = $(formEntity).find(":submit").html();

                // console.log(formData,action,method,btn_text);
                $('#errorMessageAccept').html('');
                $.ajax({
                    type: method,
                    url: action,
                    crossDomain: true,
                    dataType: 'json',
                    cache: false,
                    // contentType: "application/json", // Correct header for sending JSON data
                    contentType: false,
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': 'Bearer ' +
                            jwtToken // Include JWT token if authentication is required
                    },
                    data: formData,
                    processData: false,
                    beforeSend: function() {
                        parentForm.find(":submit").prop('disabled', true);
                        btn_text = parentForm.find(":submit").html();
                        var ht_data =
                            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                        parentForm.find(":submit").html(ht_data);
                    },
                    success: function(data) {
                        parentForm.find(":submit").prop('disabled', false);
                        parentForm.find(":submit").html(btn_text);
                        if (data.hasOwnProperty('success')) {
                            if (mode == 'add' && data.success == true) {
                                $(formEntity)[0].reset();
                                $('#medicineAcceptModal').modal('hide');
                                getList();
                            }
                        }
                    },
                    error: function(response) {
                        parentForm.find(":submit").prop('disabled', false);
                        parentForm.find(":submit").html(btn_text);
                        if (response.status === 422) {
                            var errors = response.responseJSON.errors;
                            $('#errorMessageAccept').append('<ul>');
                            $.each(errors, function(key, value) {
                                $('#errorMessageAccept').append('<li>' + value[0] + '</li>');
                            });
                            $('#errorMessageAccept').append('</ul>');
                        } else {
                            $('#infoMessage').append(
                                '<p>Something went wrong. Please try again.</p>');
                        }
                    }
                });
            });
            $(document).on("submit", "#submitFormReject", function() {
                event.preventDefault(); // Prevent the form from submitting normally
                var formEntity = $(this).closest('form');
                var formData = new FormData($(formEntity)[0]);
                if (appendField && Object.keys(appendField).length !== 0) {
                    Object.entries(appendField).forEach(([key, value]) => {
                        formData.append(key, value);
                    });
                }
                var parentForm = $(formEntity).closest('form');
                action = $(formEntity).attr("action");
                method = $(formEntity).attr("method");
                var mode = $(formEntity).attr('data-mode');
                var btn_text = $(formEntity).find(":submit").html();

                // console.log(formData,action,method,btn_text);
                $('#errorMessageReject').html('');
                $.ajax({
                    type: method,
                    url: action,
                    crossDomain: true,
                    dataType: 'json',
                    cache: false,
                    // contentType: "application/json", // Correct header for sending JSON data
                    contentType: false,
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': 'Bearer ' +
                            jwtToken // Include JWT token if authentication is required
                    },
                    data: formData,
                    processData: false,
                    beforeSend: function() {
                        parentForm.find(":submit").prop('disabled', true);
                        btn_text = parentForm.find(":submit").html();
                        var ht_data =
                            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>&nbsp;Loading...';
                        parentForm.find(":submit").html(ht_data);
                    },
                    success: function(data) {
                        parentForm.find(":submit").prop('disabled', false);
                        parentForm.find(":submit").html(btn_text);
                        if (data.hasOwnProperty('success')) {
                            if (mode == 'add' && data.success == true) {
                                $(formEntity)[0].reset();
                                $('#medicineRejectModal').modal('hide');
                                getList();
                            }
                        }
                    },
                    error: function(response) {
                        parentForm.find(":submit").prop('disabled', false);
                        parentForm.find(":submit").html(btn_text);
                        if (response.status === 422) {
                            var errors = response.responseJSON.errors;
                            $('#errorMessageReject').append('<ul>');
                            $.each(errors, function(key, value) {
                                $('#errorMessageReject').append('<li>' + value[0] + '</li>');
                            });
                            $('#errorMessageReject').append('</ul>');
                        } else {
                            $('#infoMessage').append(
                                '<p>Something went wrong. Please try again.</p>');
                        }
                    }
                });
            });
        });

        function medicineAcceptModal(parent,id) {
            $('#accept_order_id').val(id);
            $('#type_of_collectionDiv').hide();
            var data = $(parent).attr('data-id');
            data = JSON.parse(data);
            $('#full_address').val(data.full_address);
            $('#landmark').val(data.landmark);
            $('#city').val(data.city);
            $('#pincode').val(data.pincode);
            if (data.type_of_collection == 'CV') {
                $('#type_of_collection_cv').click();
            }
            else{
                $('#type_of_collection_hc').click();
            }
            // getDeleiveryInfo(data.type_of_collection);
        }
        function medicineRejectModal(parent,id) {
            $('#reject_order_id').val(id);
            $('#reject_remarksDiv').hide();
        }
        function getRejectReason(val) {
            if(val == 'Other'){
                $('#reject_remarksDiv').show();
            }
            else{
                $('#reject_remarksDiv').hide();
            }
        }
         
        function getDeleiveryInfo(val) {
            if (val == 'CV') {
                $('#type_of_collectionDiv').hide();
            } else {
                $('#type_of_collectionDiv').show();
            }
        }

        function gotoLinkByAgent(base_url) {
            let clinic_id = "{{ request('clinic_id') }}";
            let task_id = "{{ request('task_id') }}";
            let source_id = "{{ request('source_id') }}";
            window.location.href = base_url + "?clinic_id=" + clinic_id + "&task_id=" + task_id + "&source_id=" + source_id;
        }
        $("#date_range").flatpickr({
            mode: "range",
            //   maxDate: "today"
        });
        $(document).ready(function() {
            let ht_id = '#data-list';
            setId(ht_id); // set show table id
            let url = "{{ config('pharmacy.order_url') . 'list' }}";
            setListUrl(url); // api url for show table
            let method = 'POST';
            setMethod(method);
            let sortCollumns = [
                "id",
                "patients.name",
                "patient_phone",
                "clinics.clinic_name",
            ];
            setSortCollumns(sortCollumns);
            let perPage = 10; // set items per page
            itemsPerPage(perPage);
            let page = 1; // set current page
            currentPage(page);
            let search = "2"; // set search
            let filter = {
                "filter": {

                },
                "filtermulti": {

                },
                "pagination": {
                    "limit": 3,
                    "offset": 0
                },
                "sort": {
                    "id": "desc"
                }
            };
            setFilter(filter); // set filter [where, pagination, sort]
            paginationFilter(); // by default first pagination
            getList(); // get list data with pagination and filter
        });

        function updateOrderStatus(url, method, parent) {
            // console.log(url,method);
            if (!jwtToken) {
                redirectToSwal(loginUrl);
                return false;
            }
            // let field = $(parent).attr('name');
            let status = $(parent).val();
            // console.log(field, status);
            // return false;
            $.ajax({
                type: method,
                url: url,
                crossDomain: true,
                dataType: 'json',
                cache: false, // Corrected from 'catch' to 'cache'
                contentType: "application/json",
                headers: {
                    'Accept': 'application/json',
                    'Authorization': 'Bearer ' + jwtToken // Include JWT token if authentication is required
                },
                data: JSON.stringify({
                    status: status
                }), // Convert data object to JSON string
                processData: false, // Prevent jQuery from automatically transforming the data into a query string
                success: function(data) {
                    // console.log(data);
                    if (data.success == true) {
                        successAlertSwal(data);
                    } else {
                        errorAlertSwal(data);
                    }
                    getList();
                },
                error: function(response) {
                    console.error(response); // Log the error response for debugging
                    // redirectToSwal(loginUrl,response); // Uncomment if you want to redirect on error
                }
            });
        }
    </script>
@endpush
