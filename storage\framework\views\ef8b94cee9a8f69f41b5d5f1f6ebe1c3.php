<tr>
    <td colspan="10">
        <div class="d-flex align-items-center">

            <span>Showing <?php echo e(($cur_page - 1) * $itemsPerPage + 1); ?> to <?php echo e(min($cur_page * $itemsPerPage, $totalCount)); ?>

                of <?php echo e($totalCount); ?> entries</span>
            <nav class="ms-auto" aria-label="Page navigation example">
                <ul id="pagination" class="pagination">
                    <li class="page-item <?php echo e($cur_page == 1 ? 'disabled' : ''); ?>">
                        <a class="page-link" href="#" aria-label="Previous" data-page="<?php echo e($cur_page - 1); ?>">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <?php for($i = 1; $i <= $totalPages; $i++): ?>
                        <?php if($i == 1 || $i == $totalPages || ($i >= $cur_page - 2 && $i <= $cur_page + 2)): ?>
                            <?php
                                $dotted = true;
                            ?>
                            <li class="page-item <?php echo e($i == $cur_page ? 'active' : ''); ?>">
                                <a class="page-link" href="#"
                                    data-page="<?php echo e($i); ?>"><?php echo e($i); ?></a>
                            </li>
                        <?php else: ?>
                            <?php if($dotted): ?>
                                <li class="page-item">
                                    <span class="page-link" href="#">...</span>
                                </li>
                            <?php endif; ?>
                            <?php
                                $dotted = false;
                            ?>
                        <?php endif; ?>
                    <?php endfor; ?>
                    <li class="page-item <?php echo e($cur_page == $totalPages ? 'disabled' : ''); ?>">
                        <a class="page-link" href="#" aria-label="Next" data-page="<?php echo e($cur_page + 1); ?>">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </td>
</tr>
<?php /**PATH C:\wamp64\www\mymd-care\resources\views/admin/custom/pagination.blade.php ENDPATH**/ ?>