<form class="clearfix" method="post"
    action="<?php echo e($id ? config('appointment.url') . 'update/' . $id : config('appointment.url') . 'add'); ?>"
    data-mode="<?php echo e($id ? 'update' : 'add'); ?>" enctype="multipart/form-data" id="submitForm">
    <?php if(!$id): ?>
        <input type="hidden" name="patient_id" value="<?php echo e($data['patient_detail']->id); ?>">
        <input type="hidden" name="patient_phone" value="<?php echo e($phone); ?>">
    <?php endif; ?>
    <div class="row">
        <div class="form-group col-md-3">
            <label for="exampleInputEmail1" class="form-label fw-bold">Doctor <b class="text-primary">*</b></label>
            <select id="doctor_id" name="doctor_id" data-show="clinic_id" class="select2-multpl-custom1 form-select"
                data-style="py-0" onchange="changeDoctor(this)">
                <option value="">Select</option>
                <?php $__currentLoopData = $list['doctor_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($row->id); ?>" <?php echo e($id ? ($data['doctor_id'] == $row->id ? 'selected' : '') : ''); ?>>Dr. <?php echo e($row->username); ?>

                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="form-group col-md-3">
            <label for="exampleInputEmail1" class="form-label fw-bold">Clinic <b class="text-primary">*</b></label>
            <select id="clinic_id" name="clinic_id" data-show="date" class="select2-multpl-custom1 form-select"
                data-style="py-0" onchange="changeClinic(this)">
                <option value="">Select</option>
                <?php if($id): ?>
                    <?php $__currentLoopData = $list['clinic_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row['clinic_id']); ?>"
                            <?php echo e($id ? ($data['clinic_id'] == $row['clinic_id'] ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['name']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </select>
        </div>
        <div class="form-group col-md-3">
            <label for="exampleInputEmail1" class="form-label fw-bold">Date <b class="text-primary">*</b></label>
            <select id="date" name="date" data-show="time_slot" class="select2-multpl-custom1 form-select"
                data-style="py-0" onchange="changeDate(this)">
                <option value="">Select</option>
                <?php if($id): ?>
                    <?php
                        $dates = array_column($list['date_list'], 'name');
                    ?>
                    <?php if(!in_array($data['date'], $dates)): ?>
                        <option value="<?php echo e($data['date']); ?>" selected><?php echo e($data['date']); ?></option>
                    <?php endif; ?>
                    <?php $__currentLoopData = $dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($row); ?>"
                            <?php echo e($id ? ($data['date'] == $row ? 'selected' : '') : ''); ?>>
                            <?php echo e($row); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </select>
        </div>
        <div class="form-group col-md-3">
            <label for="exampleInputEmail1" class="form-label fw-bold">Available Slots <b class="text-primary">*</b></label>
            <select id="time_slot" name="time_slot" class="select2-multpl-custom1 form-select" data-style="py-0">
                <option value="">Select</option>
                <?php if($id): ?>
                    <?php $__currentLoopData = $list['time_slot_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option
                            value="<?php echo e($row['name']); ?><?php echo e(isset($row['schedule_id']) ? ',' . $row['schedule_id'] : ''); ?>"
                            <?php echo e($id ? ($data['time_slot'] == $row['name'] ? 'selected' : '') : ''); ?>>
                            <?php echo e($row['name']); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </select>
        </div>
        <?php if(!$id): ?>
            <div class="form-group col-lg-3 col-md-6 col-12">
                <label for="exampleInputEmail1" class="fw-bold">
                    Appointment Status <b class="text-primary">*</b>
                </label>
                <select class="form-select form-select-sm m-bot15" name="status" id="status">
                    <?php $__currentLoopData = $list['status_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>"><?php echo e($row); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>

            <div class="form-group col-lg-3 col-md-6 col-12">
                <label for="exampleInputEmail1" class="fw-bold">
                    Remarks <b class="text-primary">*</b>
                </label>
                <input type="text" class="form-control form-control-sm" name="remarks" id="exampleInputEmail1"
                    value='' placeholder="">
            </div>
            <div class="form-group col-lg-3 col-md-6 col-12">
                <label for="exampleInputEmail1" class="fw-bold">Appointment Type <b class="text-primary">*</b></label>
                <select class="form-select form-select-sm m-bot15" name="appointment_type" id="appointment_type">
                    <?php $__currentLoopData = $list['appointment_type_list']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>"><?php echo e($row); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
        <?php endif; ?>
        <div class="form-group col-md-12">
            <button type="submit" name="submit" class="btn btn-primary text-white">Submit</button>
        </div>
        <div class="form-group col-md-12">
            <div id="errorMessage" class="" style="color: red;"></div>
        </div>
    </div>
</form>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/api/addEdit.blade.php ENDPATH**/ ?>