<div class="row">
    
    <?php if(array_intersect(['view_doctor'], $permissionPage)): ?>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-white db-top-stat-box transition hover-shadow">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center gap-3">
                        <div class="bg-soft-primary rounded p-3 flex-shrink-1">
                            <svg class="icon-32" height="32" viewBox="0 0 100 100" width="32"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path
                                        d="m75.507 57.126c.856 1.976 1.354 4.394 1.464 7.19 2.889.662 5.051 3.246 5.051 6.332 0 3.584-2.916 6.5-6.5 6.5s-6.5-2.916-6.5-6.5c0-3.051 2.115-5.608 4.953-6.307-.125-2.777-.789-7.002-3.507-9.088-1.387-.359-2.813-.619-4.276-.753-.108 4.865-16.188 16.871-16.188 16.871s-16.085-12.004-16.194-16.869c-1.625.148-3.206.445-4.733.873-1.983 1.57-3.009 4.495-3.062 8.747.583.335 1.054.858 1.343 1.491 2.136 1.009 4.023 3.131 5.468 6.152.242.508.274 1.082.096 1.606.866 2.229 1.361 4.665 1.361 6.711 0 2.867 0 5.578-3.125 6.274-.338.281-.762.436-1.207.436h-2.088c-1.047 0-1.899-.854-1.899-1.898l.002-.074c.04-1.01.885-1.825 1.897-1.825h2.088c.214 0 .423.036.625.106.126-.035.166-.064.167-.065.232-.412.232-2.128.232-2.952 0-1.662-.416-3.669-1.145-5.534-.378-.215-.684-.54-.872-.933-1.266-2.651-2.988-4.363-4.386-4.363-1.43 0-3.238 1.852-4.499 4.604-.206.449-.567.814-1.011 1.033-.659 1.784-1.021 3.621-1.021 5.192 0 .692 0 2.528.264 2.96.003 0 .062.036.228.077.216-.083.448-.126.68-.126h2.092c.975 0 1.79.742 1.888 1.707l.01.117c0 1.121-.852 1.975-1.898 1.975h-2.092c-.415 0-.816-.139-1.146-.391-1.195-.225-2.037-.752-2.57-1.61-.646-1.037-.764-2.399-.764-4.709 0-2.026.468-4.36 1.318-6.589-.125-.477-.083-.975.125-1.424.885-1.936 2.011-3.594 3.255-4.793.684-.659 1.419-1.189 2.188-1.576.288-.674.788-1.227 1.399-1.576.032-2.665.442-4.966 1.2-6.863-8.678 4.402-14.625 13.405-14.625 23.802 0 13.286 9.707 13.936 22.414 13.936 1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936-.001-10.507-6.075-19.589-14.901-23.938z"
                                        fill="currentColor"></path>
                                    <path
                                        d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                        fill="currentColor" opacity=".4"></path>
                                    <circle cx="75.521" cy="70.648" r="3" fill="currentColor"></circle>
                                </g>
                            </svg>
                        </div>
                        <div class="text-start flex-grow-1">
                            <h2 class="counter" style="visibility: visible;"><?php echo e($data['doctor']); ?></h2>
                            Doctor
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if(array_intersect(['view_patient'], $permissionPage)): ?>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-white db-top-stat-box transition hover-shadow">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center gap-3">
                        <div class="bg-soft-primary rounded p-3 flex-shrink-1">
                            <svg class="icon-32" height="32" viewBox="0 0 100 100" width="32"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path
                                        d="m32.007 95c1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936 0-13.906-10.635-25.322-24.217-26.563-.108 4.865-16.188 16.871-16.188 16.871s-16.084-12.005-16.193-16.87c-13.58 1.24-24.217 12.656-24.217 26.562 0 13.286 9.707 13.936 22.414 13.936zm29.993-18.143c0-.396.357-.715.801-.715h4.344v-4.342c0-.44.317-.801.713-.801h4.287c.394 0 .713.358.713.801v4.343h4.342c.44 0 .8.319.8.715v4.285c0 .396-.357.715-.8.715h-4.343v4.342c0 .442-.32.8-.715.8h-4.285c-.396 0-.715-.357-.715-.8v-4.343h-4.342c-.442 0-.801-.319-.801-.715z"
                                        fill="currentColor"></path>
                                    <path
                                        d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                        fill="currentColor" opacity=".4"></path>
                                </g>
                            </svg>
                        </div>
                        <div class="text-start flex-grow-1">
                            <h2 class="counter" style="visibility: visible;"> <?php echo e($data['patient']); ?></h2>
                            Patients
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if(array_intersect(['view_appointment'], $permissionPage)): ?>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-white db-top-stat-box transition hover-shadow">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center gap-3">
                        <div class="bg-soft-primary rounded p-3 flex-shrink-1">
                            <svg width="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                class="icon-32" height="32">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M3 16.8701V9.25708H21V16.9311C21 20.0701 19.0241 22.0001 15.8628 22.0001H8.12733C4.99561 22.0001 3 20.0301 3 16.8701ZM7.95938 14.4101C7.50494 14.4311 7.12953 14.0701 7.10977 13.6111C7.10977 13.1511 7.46542 12.7711 7.91987 12.7501C8.36443 12.7501 8.72997 13.1011 8.73985 13.5501C8.7596 14.0111 8.40395 14.3911 7.95938 14.4101ZM12.0198 14.4101C11.5653 14.4311 11.1899 14.0701 11.1701 13.6111C11.1701 13.1511 11.5258 12.7711 11.9802 12.7501C12.4248 12.7501 12.7903 13.1011 12.8002 13.5501C12.82 14.0111 12.4643 14.3911 12.0198 14.4101ZM16.0505 18.0901C15.596 18.0801 15.2305 17.7001 15.2305 17.2401C15.2206 16.7801 15.5862 16.4011 16.0406 16.3911H16.0505C16.5148 16.3911 16.8902 16.7711 16.8902 17.2401C16.8902 17.7101 16.5148 18.0901 16.0505 18.0901ZM11.1701 17.2401C11.1899 17.7001 11.5653 18.0611 12.0198 18.0401C12.4643 18.0211 12.82 17.6411 12.8002 17.1811C12.7903 16.7311 12.4248 16.3801 11.9802 16.3801C11.5258 16.4011 11.1701 16.7801 11.1701 17.2401ZM7.09989 17.2401C7.11965 17.7001 7.49506 18.0611 7.94951 18.0401C8.39407 18.0211 8.74973 17.6411 8.72997 17.1811C8.72009 16.7311 8.35456 16.3801 7.90999 16.3801C7.45554 16.4011 7.09989 16.7801 7.09989 17.2401ZM15.2404 13.6011C15.2404 13.1411 15.596 12.7711 16.0505 12.7611C16.4951 12.7611 16.8507 13.1201 16.8705 13.5611C16.8804 14.0211 16.5247 14.4011 16.0801 14.4101C15.6257 14.4201 15.2503 14.0701 15.2404 13.6111V13.6011Z"
                                    fill="currentColor"></path>
                                <path opacity="0.4"
                                    d="M3.00293 9.25699C3.01577 8.66999 3.06517 7.50499 3.15803 7.12999C3.63224 5.02099 5.24256 3.68099 7.54442 3.48999H16.4555C18.7376 3.69099 20.3677 5.03999 20.8419 7.12999C20.9338 7.49499 20.9832 8.66899 20.996 9.25699H3.00293Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M8.30465 6.59C8.73934 6.59 9.06535 6.261 9.06535 5.82V2.771C9.06535 2.33 8.73934 2 8.30465 2C7.86996 2 7.54395 2.33 7.54395 2.771V5.82C7.54395 6.261 7.86996 6.59 8.30465 6.59Z"
                                    fill="currentColor"></path>
                                <path
                                    d="M15.6953 6.59C16.1201 6.59 16.456 6.261 16.456 5.82V2.771C16.456 2.33 16.1201 2 15.6953 2C15.2606 2 14.9346 2.33 14.9346 2.771V5.82C14.9346 6.261 15.2606 6.59 15.6953 6.59Z"
                                    fill="currentColor"></path>
                            </svg>
                        </div>
                        <div class="text-start flex-grow-1">
                            <h2 class="counter" style="visibility: visible;"> <?php echo e($data['appointment']); ?></h2>
                            OPD Appointments
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if(array_intersect(['view_prescription'], $permissionPage)): ?>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-white db-top-stat-box transition hover-shadow">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center gap-3">
                        <div class="bg-soft-primary rounded p-3 flex-shrink-1">
                            <svg id="fi_9597080" height="32" viewBox="0 0 64 64" width="32"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <path
                                        d="m45.07 35.67-1.1 2.7c1.69.43 3.28 1.22 4.63 **********.61.53.89.81s.54.56.79.86c1.17 1.43 1.96 3.05 2.38 4.72.7-.6 1.27-1.37 1.65-2.29l1.88-4.63-11.12-4.52z"
                                        fill="currentColor"></path>
                                    <path
                                        d="m55.51 25.88c-3.07-1.25-6.57.23-7.82 3.3l-1.88 4.63 11.12 4.52 1.88-4.64c1.25-3.07-.23-6.57-3.3-7.82z"
                                        fill="currentColor"></path>
                                </g>
                                <path
                                    d="m37 4h-28c-2.21 0-4 1.79-4 4v41c0 2.21 1.79 4 4 4h20.37c-1.04-4 .03-8.38 3.15-11.49 2.26-2.27 5.27-3.52 8.48-3.52v-29.99c0-2.21-1.79-4-4-4zm-19 10h4v-4h4v4h4v4h-4v4h-4v-4h-4zm-6.49 34h-2.55c-.55 0-1-.45-1-1s.45-1 1-1h2.55c.55 0 1 .45 1 1s-.45 1-1 1zm0-6h-2.55c-.55 0-1-.45-1-1s.45-1 1-1h2.55c.55 0 1 .45 1 1s-.45 1-1 1zm0-6h-2.55c-.55 0-1-.45-1-1s.45-1 1-1h2.55c.55 0 1 .45 1 1s-.45 1-1 1zm0-6h-2.55c-.55 0-1-.45-1-1s.45-1 1-1h2.55c.55 0 1 .45 1 1s-.45 1-1 1zm14.49 18h-11c-.55 0-1-.45-1-1s.45-1 1-1h11c.55 0 1 .45 1 1s-.45 1-1 1zm3-6h-14c-.55 0-1-.45-1-1s.45-1 1-1h14c.55 0 1 .45 1 1s-.45 1-1 1zm8-6h-22c-.55 0-1-.45-1-1s.45-1 1-1h22c.55 0 1 .45 1 1s-.45 1-1 1zm0-6h-22c-.55 0-1-.45-1-1s.45-1 1-1h22c.55 0 1 .45 1 1s-.45 1-1 1z"
                                    fill="currentColor"></path>
                                <path
                                    d="m47.33 42.25-14.08 14.08c-3.22-3.92-2.99-9.74.68-13.4 3.66-3.67 9.48-3.9 13.4-.68z"
                                    fill="currentColor" opacity="0.4"></path>
                                <path
                                    d="m48.07 57.07c-3.66 3.67-9.48 3.9-13.4.68l14.08-14.08c3.22 3.92 2.99 9.74-.68 13.4z"
                                    fill="currentColor" opacity="0.4"></path>
                            </svg>
                        </div>
                        <div class="text-start flex-grow-1">
                            <h2 class="counter" style="visibility: visible;"><?php echo e($data['prescription']); ?></h2>
                            Prescriptions
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if(array_intersect(['view_appointment'], $permissionPage)): ?>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-white db-top-stat-box transition hover-shadow">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center gap-3">
                        <div class="bg-soft-primary rounded p-3 flex-shrink-1">
                            <i class="icon">
                                <svg id="fi_2183886" enable-background="new 0 0 512 512" height="32"
                                    viewBox="0 0 512 512" width="32" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="m199.463 116h-132c-8.284 0-15 6.716-15 15 0 6.528 4.178 12.067 10 14.128v83.872h142v-83.872c5.822-2.061 10-7.6 10-14.128 0-8.284-6.716-15-15-15z"
                                        opacity="0.3" fill="currentColor"></path>
                                    <path
                                        d="m224.495 30h10.042v163.249c-.025.01-.049.02-.073.03v118.728c.12-.001.237-.008.357-.008 9.186 0 17.084 3.478 23.43 6.272 4.354 1.917 8.467 3.728 11.34 3.728 2.874 0 6.987-1.811 11.341-3.728 6.346-2.794 14.244-6.272 23.43-6.272s17.083 3.478 23.43 6.272c4.354 1.917 8.467 3.728 11.341 3.728s6.986-1.811 11.34-3.728c6.346-2.794 14.244-6.272 23.43-6.272 9.187 0 17.084 3.478 23.43 6.272 4.354 1.917 8.467 3.728 11.34 3.728s6.986-1.811 11.34-3.728c6.346-2.794 14.244-6.272 23.43-6.272 4.47 0 8.705.632 12.73 1.595-4.804-23.468-14.695-45.575-29.291-65.145-18.702-25.076-43.554-44-72.345-55.201v-163.248h10.042c8.284 0 15-6.716 15-15s-6.716-15-15-15h-140.084c-8.284 0-15 6.716-15 15s6.716 15 15 15zm103.509 223.999c8.262 0 15 6.738 15 15s-6.738 15-15 15-15-6.739-15-15c0-8.262 6.738-15 15-15z"
                                        opacity="0.3" fill="currentColor"></path>
                                    <path
                                        d="m459.53 346.224c-5.836-2.253-11.337-4.225-16.087-4.225-2.874 0-6.986 1.811-11.34 3.728-6.346 2.794-14.244 6.272-23.43 6.272s-17.083-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.341-3.728s-6.986 1.811-11.34 3.728c-6.346 2.794-14.244 6.272-23.43 6.272s-17.084-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.34-3.728s-6.986 1.811-11.34 3.728c-6.346 2.794-14.244 6.272-23.43 6.272s-17.084-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.34-3.728-.117 0-.24.017-.357.019v98.971c0 19.39-5.497 37.519-15.008 52.917 22.532 11.564 48.058 18.094 75.08 18.094 90.981 0 165-74.019 165-165 0-.259-.006-.517-.007-.776zm-175.567 56.776c-8.262 0-15-6.739-15-15 0-8.262 6.738-15 15-15s15 6.738 15 15c0 8.261-6.738 15-15 15zm55 29.999c-8.262 0-15-6.738-15-15s6.739-15 15-15c8.262 0 15 6.738 15 15s-6.738 15-15 15z"
                                        fill="currentColor"></path>
                                    <path
                                        d="m62.463 285.999h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v30h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v30.001h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v4.989c0 39.149 31.851 71 71 71 39.15 0 71-31.851 71-71v-181.989h-142z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                        </div>
                        <div class="text-start flex-grow-1">
                            <h2 class="counter" style="visibility: visible;"><?php echo e($data['test_collection']); ?></h2>
                            Diagnostic Collection
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if(array_intersect(['view_billing'], $permissionPage)): ?>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-white db-top-stat-box transition hover-shadow">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center gap-3">
                        <div class="bg-soft-primary rounded p-3 flex-shrink-1">
                            <i class="icon">
                                <svg id="fi_2183886" enable-background="new 0 0 512 512" height="32"
                                    viewBox="0 0 512 512" width="32" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="m199.463 116h-132c-8.284 0-15 6.716-15 15 0 6.528 4.178 12.067 10 14.128v83.872h142v-83.872c5.822-2.061 10-7.6 10-14.128 0-8.284-6.716-15-15-15z"
                                        opacity="0.3" fill="currentColor"></path>
                                    <path
                                        d="m224.495 30h10.042v163.249c-.025.01-.049.02-.073.03v118.728c.12-.001.237-.008.357-.008 9.186 0 17.084 3.478 23.43 6.272 4.354 1.917 8.467 3.728 11.34 3.728 2.874 0 6.987-1.811 11.341-3.728 6.346-2.794 14.244-6.272 23.43-6.272s17.083 3.478 23.43 6.272c4.354 1.917 8.467 3.728 11.341 3.728s6.986-1.811 11.34-3.728c6.346-2.794 14.244-6.272 23.43-6.272 9.187 0 17.084 3.478 23.43 6.272 4.354 1.917 8.467 3.728 11.34 3.728s6.986-1.811 11.34-3.728c6.346-2.794 14.244-6.272 23.43-6.272 4.47 0 8.705.632 12.73 1.595-4.804-23.468-14.695-45.575-29.291-65.145-18.702-25.076-43.554-44-72.345-55.201v-163.248h10.042c8.284 0 15-6.716 15-15s-6.716-15-15-15h-140.084c-8.284 0-15 6.716-15 15s6.716 15 15 15zm103.509 223.999c8.262 0 15 6.738 15 15s-6.738 15-15 15-15-6.739-15-15c0-8.262 6.738-15 15-15z"
                                        opacity="0.3" fill="currentColor"></path>
                                    <path
                                        d="m459.53 346.224c-5.836-2.253-11.337-4.225-16.087-4.225-2.874 0-6.986 1.811-11.34 3.728-6.346 2.794-14.244 6.272-23.43 6.272s-17.083-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.341-3.728s-6.986 1.811-11.34 3.728c-6.346 2.794-14.244 6.272-23.43 6.272s-17.084-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.34-3.728s-6.986 1.811-11.34 3.728c-6.346 2.794-14.244 6.272-23.43 6.272s-17.084-3.478-23.43-6.272c-4.354-1.917-8.467-3.728-11.34-3.728-.117 0-.24.017-.357.019v98.971c0 19.39-5.497 37.519-15.008 52.917 22.532 11.564 48.058 18.094 75.08 18.094 90.981 0 165-74.019 165-165 0-.259-.006-.517-.007-.776zm-175.567 56.776c-8.262 0-15-6.739-15-15 0-8.262 6.738-15 15-15s15 6.738 15 15c0 8.261-6.738 15-15 15zm55 29.999c-8.262 0-15-6.738-15-15s6.739-15 15-15c8.262 0 15 6.738 15 15s-6.738 15-15 15z"
                                        fill="currentColor"></path>
                                    <path
                                        d="m62.463 285.999h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v30h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v30.001h30.75c8.284 0 15 6.716 15 15s-6.716 15-15 15h-30.75v4.989c0 39.149 31.851 71 71 71 39.15 0 71-31.851 71-71v-181.989h-142z"
                                        fill="currentColor"></path>
                                </svg>
                            </i>
                        </div>
                        <div class="text-start flex-grow-1">
                            <h2 class="counter" style="visibility: visible;"><?php echo e($data['membership']); ?></h2>
                            Membership
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Authorization\resources/views/admin/api/dashboardCount.blade.php ENDPATH**/ ?>