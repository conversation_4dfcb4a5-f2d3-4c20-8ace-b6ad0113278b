<?php $__currentLoopData = $data['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <tr>
        <td><?php echo e($row['id']); ?></td>
        <td>
            <?php if(isset($row['patient_with_membership']['name'])): ?>
                <a href="<?php echo e(route('patient.info', [$row['patient_with_membership']['id']])); ?>"
                    class="text-decoration-underline text-dark" title="Patient History" role="button">
                    <?php echo e($row['patient_with_membership']['name']); ?>

                </a>
            <?php endif; ?>
        </td>
        <td><?php echo e($row['patient_phone']); ?></td>
        <td>
            <?php if(!empty($row['patient_with_membership']['membership_registration_opd'])): ?>
                <?php
                    $membership = $row['patient_with_membership']['membership_registration_opd'][0];
                ?>
                <div><?php echo e($membership['memberships']['name'] ?? ''); ?></div>
                <div><?php echo e($membership['registration_no'] ?? ''); ?></div>
                <div><?php echo e(date('d-m-Y', strtotime($membership['end_date']))); ?></div>
            <?php endif; ?>
        </td>
        <td><?php echo e($row['username'] ?? ''); ?></td>
        <td><?php echo e($row['time_slot']); ?> <br> <?php echo e($row['date']); ?></td>
        <td><?php echo e($row['clinic_name']); ?></td>
        <td><?php echo e($row['remarks']); ?></td>
        <td>
            <?php if(array_intersect(['change_status_appointment'], $permissionPage)): ?>
                <select class="form-select form-select-sm m-bot15 pe-4" style="min-width:130px;" name="status"
                    onchange="updateAppointmentStatus('<?php echo e(config('appointment.url') . 'updateStatus/' . $row['id']); ?>','POST',this)">
                    <?php $__currentLoopData = $status_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key2 => $row2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($key2 >= $row['status'] || $role->id == 1): ?>
                            <option value="<?php echo e($key2); ?>" <?php echo e($key2 == $row['status'] ? 'selected' : ''); ?>>
                                <?php echo e($row2); ?></option>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            <?php else: ?>
                <?php echo e($status_list[$row['status']]); ?>

            <?php endif; ?>
        </td>
        <td>
            <div class="d-flex flex-nowrap  gap-1">
                <div class="text-center mt-3  lh-1">
                    <?php if(array_intersect(['create_bill_appointment'], $permissionPage) &&
                            $row['status'] == 3 &&
                            $row['payment_status'] == 'unpaid' &&
                            strtotime(date('Y-m-d')) == strtotime($row['date']) &&
                            !in_array($row['appointment_type'], [2, 3])): ?>
                        <a href="<?php echo e(route('appointment.addBill', ['id' => $row['id']])); ?>"
                            class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                            title="Consultation Fees" role="button">
                            <svg height="20" viewBox="0 -11 493.78 493" width="20"
                                xmlns="http://www.w3.org/2000/svg" id="fi_1611154">
                                <path
                                    d="m378.351562 70.472656c.214844.015625.429688.03125.648438.03125.371094 0 .742188-.03125 1.105469-.082031 9.722656.199219 17.503906 8.128906 17.515625 17.851563 0 4.417968 3.582031 8 8 8 4.417968 0 8-3.582032 8-8-.019532-15.902344-11.089844-29.660157-26.621094-33.082032v-7.6875c0-4.417968-3.582031-8-8-8s-8 3.582032-8 8v8.050782c-16.421875 4.390624-27.046875 20.277343-24.832031 37.132812 2.214843 16.855469 16.582031 29.457031 33.582031 29.457031 9.871094 0 17.871094 8.003907 17.871094 17.875 0 9.867188-8 17.871094-17.871094 17.871094s-17.871094-8.003906-17.871094-17.871094c0-4.417969-3.582031-8-8-8-4.417968 0-8 3.582031-8 8 .019532 15.328125 10.316406 28.738281 25.121094 32.71875v8.765625c0 4.417969 3.582031 8 8 8s8-3.582031 8-8v-8.398437c16.894531-3.699219 28.289062-19.535157 26.425781-36.730469-1.859375-17.195312-16.378906-30.226562-33.675781-30.222656-9.597656.003906-17.484375-7.574219-17.863281-17.164063-.375-9.589843 6.894531-17.765625 16.464843-18.511719zm0 0">
                                </path>
                                <path
                                    d="m380.207031.390625c-49.214843 0-91.214843 32.113281-106.949219 75.113281h-198.558593c-4.398438 0-7.96875 3.964844-8 8.359375l-1.890625 280.640625h-56.597656c-4.417969 0-8.210938 3.199219-8.210938 7.625v35.613282c.101562 33.527343 26.507812 61.070312 60 62.585937v.175781h247v-.234375c2 .074219 2.824219.234375 4.089844.234375h.171875c34.664062-.054687 62.738281-28.171875 62.738281-62.835937v-180.0625c2 .109375 4.117188.167969 6.1875.167969 62.628906 0 113.59375-51.0625 113.59375-113.695313 0-62.628906-50.941406-113.6875-113.574219-113.6875zm-317.164062 454.113281h-.050781c-25.878907-.035156-46.875-20.960937-46.992188-46.84375v-27.15625h232v27.042969c.011719 16.695313 6.679688 32.699219 18.523438 44.46875.839843.839844 1.882812 1.488281 2.761718 2.488281zm294.957031-46.84375c.003906 25.835938-20.914062 46.792969-46.746094 46.84375h-.152344c-25.9375-.046875-46.972656-21.015625-47.101562-46.949218v-35.425782c.066406-2.046875-.714844-4.027344-2.164062-5.472656-1.449219-1.445312-3.429688-2.222656-5.472657-2.152344h-175.554687l1.835937-273h186.171875c-1.417968 7.324219-2.152344 14.761719-2.191406 22.21875-.015625 15.769532 3.273438 31.363282 9.65625 45.78125h-75.5625c-4.421875 0-8 3.582032-8 8 0 4.417969 3.578125 8 8 8h84.242188c16.503906 25.953125 42.886718 44.046875 73.039062 50.101563zm22.207031-195.882812c-53.890625 0-97.582031-43.6875-97.578125-97.582032 0-53.894531 43.6875-97.582031 97.582032-97.582031 53.890624 0 97.578124 43.691407 97.578124 97.582031-.058593 53.867188-43.710937 97.523438-97.582031 97.582032zm0 0">
                                </path>
                                <path
                                    d="m149.367188 212.746094c-14.121094 0-25.605469 11.121094-25.605469 24.792968 0 13.671876 11.484375 24.792969 25.605469 24.792969 14.121093 0 25.609374-11.121093 25.609374-24.792969 0-13.671874-11.488281-24.792968-25.609374-24.792968zm0 33.585937c-5.300782 0-9.605469-3.945312-9.605469-8.792969 0-4.851562 4.308593-8.792968 9.605469-8.792968 5.296874 0 9.609374 3.945312 9.609374 8.792968 0 4.847657-4.3125 8.792969-9.609374 8.792969zm0 0">
                                </path>
                                <path
                                    d="m192.71875 237.503906c0 4.417969 3.578125 8 8 8h106.65625c4.417969 0 8-3.582031 8-8 0-4.417968-3.582031-8-8-8h-106.65625c-4.421875 0-8 3.582032-8 8zm0 0">
                                </path>
                                <path
                                    d="m149.367188 143.203125c-14.121094 0-25.605469 11.125-25.605469 24.796875s11.484375 24.792969 25.605469 24.792969c14.121093 0 25.609374-11.121094 25.609374-24.792969s-11.488281-24.796875-25.609374-24.796875zm0 33.589844c-5.300782 0-9.605469-3.945313-9.605469-8.792969s4.308593-8.796875 9.605469-8.796875c5.296874 0 9.609374 3.945313 9.609374 8.796875 0 4.847656-4.3125 8.796875-9.609374 8.796875zm0 0">
                                </path>
                                <path
                                    d="m149.367188 282.28125c-14.121094 0-25.605469 11.121094-25.605469 24.792969s11.484375 24.792969 25.605469 24.792969c14.121093 0 25.609374-11.121094 25.609374-24.792969s-11.488281-24.792969-25.609374-24.792969zm0 33.585938c-5.300782 0-9.605469-3.941407-9.605469-8.792969 0-4.847657 4.308593-8.792969 9.605469-8.792969 5.296874 0 9.609374 3.945312 9.609374 8.792969 0 4.847656-4.3125 8.792969-9.609374 8.792969zm0 0">
                                </path>
                                <path
                                    d="m307.375 299.503906h-106.65625c-4.421875 0-8 3.582032-8 8 0 4.417969 3.578125 8 8 8h106.65625c4.417969 0 8-3.582031 8-8 0-4.417968-3.582031-8-8-8zm0 0">
                                </path>
                            </svg>
                        </a>
                    <?php endif; ?>
                    <?php if(array_intersect(['view_bill_appointment'], $permissionPage) && $row['payment_status'] == 'paid'): ?>
                        <a target="_blank" title="Consultation Invoice"
                            class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                            role="button" href="<?php echo e(route('appointment.viewBill', [$row['id']])); ?>">
                            <svg id="fi_7377200" height="20" viewBox="0 0 60 60" width="20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="m50 32.577v-26.592a6.007 6.007 0 0 0 -6-6h-38a6.021 6.021 0 0 0 -6 6v16a2 2 0 0 0 2 2h8v26.375a1.992 1.992 0 0 0 3.062 1.686l2.694-1.691 3.693 2.332a2.014 2.014 0 0 0 2.125 0l3.692-2.33 3.667 2.318a1.981 1.981 0 0 0 2.14 0l2.018-1.275a14 14 0 1 0 16.909-18.823zm-40-10.59h-8v-16a4.018 4.018 0 0 1 4.01-4h.079a2.394 2.394 0 0 1 .311.013 4.152 4.152 0 0 1 3.6 4.2zm20 29-3.667-2.319a1.986 1.986 0 0 0 -2.141 0l-3.681 2.33-3.688-2.328a1.977 1.977 0 0 0 -2.139 0l-2.684 1.69v-44.16a6.358 6.358 0 0 0 -1.6-4.211h33.6a4 4 0 0 1 4 4v26.158a13.939 13.939 0 0 0 -15.551 17.3zm16 7.003a12 12 0 1 1 12-12 12.014 12.014 0 0 1 -12 12z">
                                </path>
                                <path d="m16 21.987h28a1 1 0 0 0 0-2h-28a1 1 0 0 0 0 2z"></path>
                                <path d="m30 39.988h-14a1 1 0 0 0 0 2h14a1 1 0 0 0 0-2z"></path>
                                <path d="m16 26.987h3a1 1 0 0 0 0-2h-3a1 1 0 1 0 0 2z"></path>
                                <path d="m40 24.987h-17a1 1 0 0 0 0 2h17a1 1 0 0 0 0-2z"></path>
                                <path d="m16 31.988h3a1 1 0 0 0 0-2h-3a1 1 0 0 0 0 2z"></path>
                                <path d="m39 29.987h-16a1 1 0 0 0 0 2h16a1 1 0 0 0 0-2z"></path>
                                <path d="m16 36.988h3a1 1 0 0 0 0-2h-3a1 1 0 0 0 0 2z"></path>
                                <path d="m33 34.988h-10a1 1 0 0 0 0 2h10a1 1 0 0 0 0-2z"></path>
                                <path
                                    d="m19 11.985a1 1 0 1 1 -.867 1.5 1 1 0 1 0 -1.731 1 2.993 2.993 0 0 0 1.6 1.3v.2a1 1 0 1 0 2 0v-.185a2.993 2.993 0 0 0 -1-5.817 1 1 0 1 1 .867-1.5 1 1 0 0 0 1.731-1 2.993 2.993 0 0 0 -1.6-1.303v-.195a1 1 0 1 0 -2 0v.184a2.993 2.993 0 0 0 1 5.816z">
                                </path>
                                <path d="m26 11.985h9a1 1 0 0 0 0-2h-9a1 1 0 0 0 0 2z"></path>
                                <path d="m26 16.986h18a1 1 0 0 0 0-2h-18a1 1 0 0 0 0 2z"></path>
                                <path
                                    d="m50.181 40.415-6.317 9.025-2.157-2.158a1 1 0 0 0 -1.414 1.418l3 3a1 1 0 0 0 .707.29.844.844 0 0 0 .087 0 1 1 0 0 0 .732-.423l7-10a1 1 0 1 0 -1.638-1.147z">
                                </path>
                            </svg>
                        </a>
                        <small class="">Paid</small>
                    <?php endif; ?>
                </div>
                <?php if(array_intersect(['add_diagnostic_bill_appointment'], $permissionPage) &&
                        $row['prescription_id'] &&
                        count($row['sample_collection_map_source']) == 0 &&
                        count($row['prescription']['prescription_child_tests']) > 0): ?>
                    <a title="Diagnostic Bill"
                        class="btn btn-light mt-3 icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" href="<?php echo e(route('diagnostic.addForm', ['s_id' => $row['id'],'type'=>'OPD'])); ?>">
                        <svg id="fi_10192569" height="20" viewBox="0 0 64 64" width="20"
                            xmlns="http://www.w3.org/2000/svg">
                            <g
                                style="fill:none;stroke:#221f1f;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-width:2">
                                <path
                                    d="m43.6 6.8h6.9c2.1 0 3.9 1.7 3.9 3.9v48.4c0 2.1-1.7 3.9-3.9 3.9h-37.1c-2.1 0-3.9-1.7-3.9-3.9v-48.4c0-2.1 1.7-3.9 3.9-3.9h6.9">
                                </path>
                                <path
                                    d="m20.4 11.4-4 .3c-1.1 0-2 .9-2 2v42.5c0 1.1.9 2 2 2h31.2c1.1 0 2-.9 2-2v-42.5c0-1.1-.9-2-2-2l-4-.3">
                                </path>
                                <path
                                    d="m43.6 7.1v4.3c0 1.1-.9 2-2 2h-19.2c-1.1 0-2-.9-2-2v-4.3c0-1.1.9-2 2-2h5.5c0-2.3 1.8-4.1 4.1-4.1s4.1 1.8 4.1 4.1h5.5c1.1 0 2 .9 2 2z">
                                </path>
                                <path d="m19.6 23.2 1.8 1.5 3.6-3.9"></path>
                                <path d="m30.6 23.3h13.8"></path>
                                <path d="m19.6 31.9 1.8 1.5 3.6-3.9"></path>
                                <path d="m30.6 32h13.8"></path>
                                <path d="m19.6 40.5 1.8 1.6 3.6-3.9"></path>
                                <path d="m30.6 40.7h13.8"></path>
                                <path d="m19.6 49.2 1.8 1.6 3.6-3.9"></path>
                                <path d="m30.6 49.3h13.8"></path>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
            </div>
        </td>
        <td>
            <div class="d-flex flex-nowrap gap-2">
                <?php if(array_intersect(['edit_appointment'], $permissionPage) &&
                        in_array($row['status'], [1, 2, 3]) &&
                        $row['payment_status'] == 'unpaid'): ?>
                    <a href="<?php echo e(route('appointment.addForm', ['id' => $row['id']])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="Edit" role="button">
                        <svg width="32" class="icon-16" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.4"
                                d="M19.9927 18.9534H14.2984C13.7429 18.9534 13.291 19.4124 13.291 19.9767C13.291 20.5422 13.7429 21.0001 14.2984 21.0001H19.9927C20.5483 21.0001 21.0001 20.5422 21.0001 19.9767C21.0001 19.4124 20.5483 18.9534 19.9927 18.9534Z"
                                fill="currentColor"></path>
                            <path
                                d="M10.309 6.90385L15.7049 11.2639C15.835 11.3682 15.8573 11.5596 15.7557 11.6929L9.35874 20.0282C8.95662 20.5431 8.36402 20.8344 7.72908 20.8452L4.23696 20.8882C4.05071 20.8903 3.88775 20.7613 3.84542 20.5764L3.05175 17.1258C2.91419 16.4915 3.05175 15.8358 3.45388 15.3306L9.88256 6.95545C9.98627 6.82108 10.1778 6.79743 10.309 6.90385Z"
                                fill="currentColor"></path>
                            <path opacity="0.4"
                                d="M18.1208 8.66544L17.0806 9.96401C16.9758 10.0962 16.7874 10.1177 16.6573 10.0124C15.3927 8.98901 12.1545 6.36285 11.2561 5.63509C11.1249 5.52759 11.1069 5.33625 11.2127 5.20295L12.2159 3.95706C13.126 2.78534 14.7133 2.67784 15.9938 3.69906L17.4647 4.87078C18.0679 5.34377 18.47 5.96726 18.6076 6.62299C18.7663 7.3443 18.597 8.0527 18.1208 8.66544Z"
                                fill="currentColor"></path>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['assign_doctor_appointment'], $permissionPage) &&
                        $row['appointment_type'] == 2 &&
                        $row['status'] < 5): ?>
                    <a href="<?php echo e(route('appointment.assignDoctor', ['id' => $row['id']])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="Assign Doctor" role="button">
                        <svg class="icon-16" height="20" viewBox="0 0 100 100" width="20"
                            xmlns="http://www.w3.org/2000/svg">
                            <g>
                                <path
                                    d="m75.507 57.126c.856 1.976 1.354 4.394 1.464 7.19 2.889.662 5.051 3.246 5.051 6.332 0 3.584-2.916 6.5-6.5 6.5s-6.5-2.916-6.5-6.5c0-3.051 2.115-5.608 4.953-6.307-.125-2.777-.789-7.002-3.507-9.088-1.387-.359-2.813-.619-4.276-.753-.108 4.865-16.188 16.871-16.188 16.871s-16.085-12.004-16.194-16.869c-1.625.148-3.206.445-4.733.873-1.983 1.57-3.009 4.495-3.062 8.747.583.335 1.054.858 1.343 1.491 2.136 1.009 4.023 3.131 5.468 6.152.242.508.274 1.082.096 1.606.866 2.229 1.361 4.665 1.361 6.711 0 2.867 0 5.578-3.125 6.274-.338.281-.762.436-1.207.436h-2.088c-1.047 0-1.899-.854-1.899-1.898l.002-.074c.04-1.01.885-1.825 1.897-1.825h2.088c.214 0 .423.036.625.106.126-.035.166-.064.167-.065.232-.412.232-2.128.232-2.952 0-1.662-.416-3.669-1.145-5.534-.378-.215-.684-.54-.872-.933-1.266-2.651-2.988-4.363-4.386-4.363-1.43 0-3.238 1.852-4.499 4.604-.206.449-.567.814-1.011 1.033-.659 1.784-1.021 3.621-1.021 5.192 0 .692 0 2.528.264 2.96.003 0 .062.036.228.077.216-.083.448-.126.68-.126h2.092c.975 0 1.79.742 1.888 1.707l.01.117c0 1.121-.852 1.975-1.898 1.975h-2.092c-.415 0-.816-.139-1.146-.391-1.195-.225-2.037-.752-2.57-1.61-.646-1.037-.764-2.399-.764-4.709 0-2.026.468-4.36 1.318-6.589-.125-.477-.083-.975.125-1.424.885-1.936 2.011-3.594 3.255-4.793.684-.659 1.419-1.189 2.188-1.576.288-.674.788-1.227 1.399-1.576.032-2.665.442-4.966 1.2-6.863-8.678 4.402-14.625 13.405-14.625 23.802 0 13.286 9.707 13.936 22.414 13.936 1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936-.001-10.507-6.075-19.589-14.901-23.938z"
                                    fill="currentColor"></path>
                                <path
                                    d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                    fill="currentColor" opacity=".4"></path>
                                <circle cx="75.521" cy="70.648" r="3" fill="currentColor"></circle>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['assign_doctor_appointment'], $permissionPage) &&
                        $row['appointment_type'] == 3 &&
                        $row['status'] < 5): ?>
                    <a href="<?php echo e(route('appointment.assignDoctorVideo', ['id' => $row['id']])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="Assign Doctor" role="button">
                        <svg class="icon-16" height="20" viewBox="0 0 100 100" width="20"
                            xmlns="http://www.w3.org/2000/svg">
                            <g>
                                <path
                                    d="m75.507 57.126c.856 1.976 1.354 4.394 1.464 7.19 2.889.662 5.051 3.246 5.051 6.332 0 3.584-2.916 6.5-6.5 6.5s-6.5-2.916-6.5-6.5c0-3.051 2.115-5.608 4.953-6.307-.125-2.777-.789-7.002-3.507-9.088-1.387-.359-2.813-.619-4.276-.753-.108 4.865-16.188 16.871-16.188 16.871s-16.085-12.004-16.194-16.869c-1.625.148-3.206.445-4.733.873-1.983 1.57-3.009 4.495-3.062 8.747.583.335 1.054.858 1.343 1.491 2.136 1.009 4.023 3.131 5.468 6.152.242.508.274 1.082.096 1.606.866 2.229 1.361 4.665 1.361 6.711 0 2.867 0 5.578-3.125 6.274-.338.281-.762.436-1.207.436h-2.088c-1.047 0-1.899-.854-1.899-1.898l.002-.074c.04-1.01.885-1.825 1.897-1.825h2.088c.214 0 .423.036.625.106.126-.035.166-.064.167-.065.232-.412.232-2.128.232-2.952 0-1.662-.416-3.669-1.145-5.534-.378-.215-.684-.54-.872-.933-1.266-2.651-2.988-4.363-4.386-4.363-1.43 0-3.238 1.852-4.499 4.604-.206.449-.567.814-1.011 1.033-.659 1.784-1.021 3.621-1.021 5.192 0 .692 0 2.528.264 2.96.003 0 .062.036.228.077.216-.083.448-.126.68-.126h2.092c.975 0 1.79.742 1.888 1.707l.01.117c0 1.121-.852 1.975-1.898 1.975h-2.092c-.415 0-.816-.139-1.146-.391-1.195-.225-2.037-.752-2.57-1.61-.646-1.037-.764-2.399-.764-4.709 0-2.026.468-4.36 1.318-6.589-.125-.477-.083-.975.125-1.424.885-1.936 2.011-3.594 3.255-4.793.684-.659 1.419-1.189 2.188-1.576.288-.674.788-1.227 1.399-1.576.032-2.665.442-4.966 1.2-6.863-8.678 4.402-14.625 13.405-14.625 23.802 0 13.286 9.707 13.936 22.414 13.936 1.387 0 2.807-.008 4.258-.008h27.467c1.449 0 2.869.008 4.256.008 12.709 0 22.42-.65 22.42-13.936-.001-10.507-6.075-19.589-14.901-23.938z"
                                    fill="currentColor"></path>
                                <path
                                    d="m50.008 57.992c12.284 0 22.241-18.471 22.241-30.754 0-12.281-9.957-22.238-22.241-22.238-12.282 0-22.239 9.957-22.239 22.238 0 12.283 9.957 30.754 22.239 30.754z"
                                    fill="currentColor" opacity=".4"></path>
                                <circle cx="75.521" cy="70.648" r="3" fill="currentColor"></circle>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['zoom_meeting_appointment'], $permissionPage) &&
                        $row['appointment_type'] == 3 &&
                        $row['meeting_id'] &&
                        $row['status'] < 5): ?>
                    <a href="<?php echo e(route('appointment.doctorVideoConsultation', ['id' => $row['id']])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="Join Meeting" target="_blank" role="button">
                        <svg clip-rule="evenodd" fill-rule="evenodd" height="24" image-rendering="optimizeQuality"
                            shape-rendering="geometricPrecision" text-rendering="geometricPrecision"
                            viewBox="0 0 512 512" width="24" xmlns="http://www.w3.org/2000/svg"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            xmlns:xodm="http://www.corel.com/coreldraw/odm/2003" id="fi_4401470">
                            <linearGradient id="id0" gradientUnits="userSpaceOnUse" x1="67.83"
                                x2="474.19" y1="82.42" y2="389.98">
                                <stop offset="0" stop-color="#4a8cff"></stop>
                                <stop offset="1" stop-color="#23b7ec"></stop>
                            </linearGradient>
                            <g id="Layer_x0020_1">
                                <g id="_1639424393248">
                                    <path
                                        d="m256 0c141.39 0 256 114.61 256 256s-114.61 256-256 256-256-114.61-256-256 114.61-256 256-256z"
                                        fill="url(#id0)"></path>
                                    <path
                                        d="m117.44 188.39v101.48c.1 22.95 18.84 41.41 41.69 41.32h147.93c4.21 0 7.59-3.38 7.59-7.49v-101.49c-.09-22.94-18.83-41.41-41.69-41.32h-147.93c-4.2 0-7.59 3.38-7.59 7.5zm206.63 39.58 61.07-44.61c5.3-4.39 9.42-3.29 9.42 4.66v136.04c0 9.05-5.03 7.96-9.42 4.67l-61.07-44.53z"
                                        fill="#fff" fill-rule="nonzero"></path>
                                </g>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['create_vital_appointment'], $permissionPage) &&
                        $row['status'] == 3 &&
                        $row['payment_status'] == 'paid' &&
                        strtotime(date('Y-m-d')) == strtotime($row['date'])): ?>
                    <?php
                        $vital_id = $row['vital_id'];
                    ?>
                    <a href="<?php echo e(route('appointment.addVital', ['id' => $row['id'], 'vital_id' => $vital_id])); ?>"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        title="<?php echo e($vital_id ? 'Edit' : 'Add'); ?> Vitals" role="button">
                        <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor" class="icon-16">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4">
                            </path>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['create_prescription'], $permissionPage) &&
                        in_array($row['status'], [4, 5, 8]) &&
                        strtotime(date('Y-m-d')) == strtotime($row['date'])): ?>
                    <?php
                        $prescription_id = $row['prescription_id'];
                    ?>
                    <a href="<?php echo e(route('prescription.addPrescription', ['id' => $row['id']])); ?>"
                        title="<?php echo e($prescription_id && $row['prescription_doctor_id'] ? 'Edit' : 'Add'); ?> Prescription"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button"
                        onclick="return previousPrescriptionModal(this,'<?php echo e($row['previous_prescription_id']); ?>','<?php echo e($row['prescription_doctor_id']); ?>')">
                        <svg class="icon-16" width="16" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M8.92574 16.39H14.3119C14.7178 16.39 15.0545 16.05 15.0545 15.64C15.0545 15.23 14.7178 14.9 14.3119 14.9H8.92574C8.5198 14.9 8.18317 15.23 8.18317 15.64C8.18317 16.05 8.5198 16.39 8.92574 16.39ZM12.2723 9.9H8.92574C8.5198 9.9 8.18317 10.24 8.18317 10.65C8.18317 11.06 8.5198 11.39 8.92574 11.39H12.2723C12.6782 11.39 13.0149 11.06 13.0149 10.65C13.0149 10.24 12.6782 9.9 12.2723 9.9ZM19.3381 9.02561C19.5708 9.02292 19.8242 9.02 20.0545 9.02C20.302 9.02 20.5 9.22 20.5 9.47V17.51C20.5 19.99 18.5099 22 16.0545 22H8.17327C5.59901 22 3.5 19.89 3.5 17.29V6.51C3.5 4.03 5.5 2 7.96535 2H13.2525C13.5099 2 13.7079 2.21 13.7079 2.46V5.68C13.7079 7.51 15.203 9.01 17.0149 9.02C17.4381 9.02 17.8112 9.02316 18.1377 9.02593C18.3917 9.02809 18.6175 9.03 18.8168 9.03C18.9578 9.03 19.1405 9.02789 19.3381 9.02561ZM19.6111 7.566C18.7972 7.569 17.8378 7.566 17.1477 7.559C16.0527 7.559 15.1507 6.648 15.1507 5.542V2.906C15.1507 2.475 15.6685 2.261 15.9646 2.572C16.5004 3.13476 17.2368 3.90834 17.9699 4.67837C18.7009 5.44632 19.4286 6.21074 19.9507 6.759C20.2398 7.062 20.0279 7.565 19.6111 7.566Z"
                                fill="currentColor"></path>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['view_prescription'], $permissionPage) &&
                        in_array($row['status'], [4, 5, 6, 7, 8]) &&
                        $row['prescription_id'] &&
                        $row['prescription_doctor_id']
                ): ?>
                    <a href="<?php echo e(route('prescription.viewPrescription', [$row['id']])); ?>" title="View prescription"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" target="_blank">
                        <svg class="icon-16" width="16" viewBox="0 0 24 24" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M12.1535 16.64L14.995 13.77C15.2822 13.47 15.2822 13 14.9851 12.71C14.698 12.42 14.2327 12.42 13.9455 12.71L12.3713 14.31V9.49C12.3713 9.07 12.0446 8.74 11.6386 8.74C11.2327 8.74 10.896 9.07 10.896 9.49V14.31L9.32178 12.71C9.03465 12.42 8.56931 12.42 8.28218 12.71C7.99505 13 7.99505 13.47 8.28218 13.77L11.1139 16.64C11.1832 16.71 11.2624 16.76 11.3515 16.8C11.4406 16.84 11.5396 16.86 11.6386 16.86C11.7376 16.86 11.8267 16.84 11.9158 16.8C12.005 16.76 12.0842 16.71 12.1535 16.64ZM19.3282 9.02561C19.5609 9.02292 19.8143 9.02 20.0446 9.02C20.302 9.02 20.5 9.22 20.5 9.47V17.51C20.5 19.99 18.5 22 16.0446 22H8.17327C5.58911 22 3.5 19.89 3.5 17.29V6.51C3.5 4.03 5.4901 2 7.96535 2H13.2525C13.5 2 13.7079 2.21 13.7079 2.46V5.68C13.7079 7.51 15.1931 9.01 17.0149 9.02C17.4333 9.02 17.8077 9.02318 18.1346 9.02595C18.3878 9.02809 18.6125 9.03 18.8069 9.03C18.9479 9.03 19.1306 9.02789 19.3282 9.02561ZM19.6045 7.5661C18.7916 7.5691 17.8322 7.5661 17.1421 7.5591C16.047 7.5591 15.145 6.6481 15.145 5.5421V2.9061C15.145 2.4751 15.6629 2.2611 15.9579 2.5721C16.7203 3.37199 17.8873 4.5978 18.8738 5.63395C19.2735 6.05379 19.6436 6.44249 19.945 6.7591C20.2342 7.0621 20.0223 7.5651 19.6045 7.5661Z"
                                fill="currentColor"></path>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['print_prescription'], $permissionPage) &&
                        in_array($row['status'], [4, 5, 6, 7, 8]) &&
                        $row['prescription_id'] &&
                        $row['prescription_doctor_id']
                ): ?>
                    <a href="<?php echo e(route('prescription.printPrescription', [$row['id']])); ?>"
                        title="Print E-prescription"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button" target="_blank">
                        <svg version="1.1" id="fi_446991" xmlns="http://www.w3.org/2000/svg" height="16"
                            width="16" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                            viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve">
                            <g>
                                <g>
                                    <path
                                        d="M329.956,399.834H182.044c-9.425,0-17.067,7.641-17.067,17.067s7.641,17.067,17.067,17.067h147.911			c9.425,0,17.067-7.641,17.067-17.067S339.381,399.834,329.956,399.834z">
                                    </path>
                                </g>
                            </g>
                            <g>
                                <g>
                                    <path
                                        d="M329.956,346.006H182.044c-9.425,0-17.067,7.641-17.067,17.067s7.641,17.067,17.067,17.067h147.911	c9.425,0,17.067-7.641,17.067-17.067S339.381,346.006,329.956,346.006z">
                                    </path>
                                </g>
                            </g>
                            <g>
                                <g>
                                    <path
                                        d="M472.178,133.907h-54.303V35.132c0-9.425-7.641-17.067-17.067-17.067H111.192c-9.425,0-17.067,7.641-17.067,17.067v98.775		H39.822C17.864,133.907,0,151.772,0,173.73v171.702c0,21.958,17.864,39.822,39.822,39.822h54.306v91.614			c0,9.425,7.641,17.067,17.067,17.067h289.61c9.425,0,17.067-7.641,17.067-17.067v-91.614h54.306			c21.958,0,39.822-17.864,39.822-39.822V173.73C512,151.773,494.136,133.907,472.178,133.907z M128.258,52.199h255.483v81.708			H128.258V52.199z M383.738,459.801H128.262c0-3.335,0-135.503,0-139.628h255.477C383.738,324.402,383.738,456.594,383.738,459.801			z M400.808,234.122h-43.443c-9.425,0-17.067-7.641-17.067-17.067s7.641-17.067,17.067-17.067h43.443			c9.425,0,17.067,7.641,17.067,17.067S410.234,234.122,400.808,234.122z">
                                    </path>
                                </g>
                            </g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                            <g></g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['upload_prescription'], $permissionPage) &&
                        $row['status'] == 8 &&
                        !$row['prescription_id'] &&
                        !$row['prescription_doctor_id'] &&
                        !$row['e_prescription_upload_fairbase']
                ): ?>
                    <a href="<?php echo e(route('prescription.uploadPrescription', [$row['id']])); ?>"
                        title="Upload prescription"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button">
                        <svg xmlns="http://www.w3.org/2000/svg" height="16" version="1.1"
                            viewBox="-23 0 512 512.00072" width="16" id="fi_1092000">
                            <g id="surface1">
                                <path
                                    d="M 348.945312 221.640625 L 348.945312 124.746094 C 348.945312 121.972656 347.664062 119.410156 345.851562 117.382812 L 237.21875 3.308594 C 235.191406 1.175781 232.308594 0 229.429688 0 L 57.195312 0 C 25.398438 0 0 25.929688 0 57.730469 L 0 383.414062 C 0 415.214844 25.398438 440.71875 57.195312 440.71875 L 193.148438 440.71875 C 218.863281 483.402344 265.605469 512 318.851562 512 C 399.738281 512 465.792969 446.265625 465.792969 365.273438 C 465.902344 294.523438 415.105469 235.40625 348.945312 221.640625 Z M 240.101562 37.457031 L 312.984375 114.179688 L 265.710938 114.179688 C 251.625 114.179688 240.101562 102.550781 240.101562 88.464844 Z M 57.195312 419.375 C 37.242188 419.375 21.34375 403.367188 21.34375 383.414062 L 21.34375 57.730469 C 21.34375 37.667969 37.242188 21.34375 57.195312 21.34375 L 218.757812 21.34375 L 218.757812 88.464844 C 218.757812 114.394531 239.78125 135.523438 265.710938 135.523438 L 327.601562 135.523438 L 327.601562 218.863281 C 324.402344 218.757812 321.839844 218.4375 319.066406 218.4375 C 281.824219 218.4375 247.570312 232.738281 221.746094 255.148438 L 86.222656 255.148438 C 80.351562 255.148438 75.550781 259.949219 75.550781 265.816406 C 75.550781 271.6875 80.351562 276.488281 86.222656 276.488281 L 201.898438 276.488281 C 194.320312 287.160156 188.023438 297.832031 183.117188 309.570312 L 86.222656 309.570312 C 80.351562 309.570312 75.550781 314.371094 75.550781 320.242188 C 75.550781 326.109375 80.351562 330.914062 86.222656 330.914062 L 176.179688 330.914062 C 173.511719 341.585938 172.125 353.429688 172.125 365.273438 C 172.125 384.480469 175.859375 403.476562 182.582031 419.484375 L 57.195312 419.484375 Z M 318.960938 490.765625 C 249.8125 490.765625 193.574219 434.527344 193.574219 365.378906 C 193.574219 296.230469 249.703125 239.992188 318.960938 239.992188 C 388.214844 239.992188 444.34375 296.230469 444.34375 365.378906 C 444.34375 434.527344 388.109375 490.765625 318.960938 490.765625 Z M 318.960938 490.765625 "
                                    style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                    fill="currentColor"></path>
                                <path
                                    d="M 86.222656 223.027344 L 194.320312 223.027344 C 200.191406 223.027344 204.992188 218.222656 204.992188 212.355469 C 204.992188 206.484375 200.191406 201.683594 194.320312 201.683594 L 86.222656 201.683594 C 80.351562 201.683594 75.550781 206.484375 75.550781 212.355469 C 75.550781 218.222656 80.351562 223.027344 86.222656 223.027344 Z M 86.222656 223.027344 "
                                    style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                    fill="currentColor"></path>
                                <path
                                    d="M 373.59375 363.136719 L 329.738281 410.410156 L 329.738281 293.882812 C 329.738281 288.011719 324.933594 283.210938 319.066406 283.210938 C 313.195312 283.210938 308.394531 288.011719 308.394531 293.882812 L 308.394531 410.410156 L 264.214844 363.136719 C 260.160156 358.871094 253.332031 358.550781 249.0625 362.605469 C 244.792969 366.660156 244.472656 373.382812 248.53125 377.652344 L 310.957031 444.773438 C 312.984375 446.90625 315.757812 448.1875 318.746094 448.1875 C 321.734375 448.1875 324.507812 446.90625 326.535156 444.773438 L 389.070312 377.652344 C 393.125 373.382812 392.910156 366.554688 388.640625 362.605469 C 384.265625 358.550781 377.652344 358.871094 373.59375 363.136719 Z M 373.59375 363.136719 "
                                    style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                    fill="currentColor"></path>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
                <?php if(array_intersect(['download_prescription'], $permissionPage) &&
                        $row['status'] == 8 &&
                        !$row['prescription_id'] &&
                        !$row['prescription_doctor_id'] &&
                        $row['e_prescription_upload_fairbase']
                ): ?>
                    <a target="_blank" title="Download Prescription"
                        class="btn btn-light icon-32 p-0 rounded-circle d-flex align-items-center justify-content-center"
                        role="button"
                        href="<?php echo e(Storage::disk('gcs')->url($row['e_prescription_upload_fairbase'])); ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" height="16" version="1.1"
                            viewBox="-23 0 512 512.00072" width="16" id="fi_1092000">
                            <g id="surface1">
                                <path
                                    d="M 348.945312 221.640625 L 348.945312 124.746094 C 348.945312 121.972656 347.664062 119.410156 345.851562 117.382812 L 237.21875 3.308594 C 235.191406 1.175781 232.308594 0 229.429688 0 L 57.195312 0 C 25.398438 0 0 25.929688 0 57.730469 L 0 383.414062 C 0 415.214844 25.398438 440.71875 57.195312 440.71875 L 193.148438 440.71875 C 218.863281 483.402344 265.605469 512 318.851562 512 C 399.738281 512 465.792969 446.265625 465.792969 365.273438 C 465.902344 294.523438 415.105469 235.40625 348.945312 221.640625 Z M 240.101562 37.457031 L 312.984375 114.179688 L 265.710938 114.179688 C 251.625 114.179688 240.101562 102.550781 240.101562 88.464844 Z M 57.195312 419.375 C 37.242188 419.375 21.34375 403.367188 21.34375 383.414062 L 21.34375 57.730469 C 21.34375 37.667969 37.242188 21.34375 57.195312 21.34375 L 218.757812 21.34375 L 218.757812 88.464844 C 218.757812 114.394531 239.78125 135.523438 265.710938 135.523438 L 327.601562 135.523438 L 327.601562 218.863281 C 324.402344 218.757812 321.839844 218.4375 319.066406 218.4375 C 281.824219 218.4375 247.570312 232.738281 221.746094 255.148438 L 86.222656 255.148438 C 80.351562 255.148438 75.550781 259.949219 75.550781 265.816406 C 75.550781 271.6875 80.351562 276.488281 86.222656 276.488281 L 201.898438 276.488281 C 194.320312 287.160156 188.023438 297.832031 183.117188 309.570312 L 86.222656 309.570312 C 80.351562 309.570312 75.550781 314.371094 75.550781 320.242188 C 75.550781 326.109375 80.351562 330.914062 86.222656 330.914062 L 176.179688 330.914062 C 173.511719 341.585938 172.125 353.429688 172.125 365.273438 C 172.125 384.480469 175.859375 403.476562 182.582031 419.484375 L 57.195312 419.484375 Z M 318.960938 490.765625 C 249.8125 490.765625 193.574219 434.527344 193.574219 365.378906 C 193.574219 296.230469 249.703125 239.992188 318.960938 239.992188 C 388.214844 239.992188 444.34375 296.230469 444.34375 365.378906 C 444.34375 434.527344 388.109375 490.765625 318.960938 490.765625 Z M 318.960938 490.765625 "
                                    style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                    fill="currentColor"></path>
                                <path
                                    d="M 86.222656 223.027344 L 194.320312 223.027344 C 200.191406 223.027344 204.992188 218.222656 204.992188 212.355469 C 204.992188 206.484375 200.191406 201.683594 194.320312 201.683594 L 86.222656 201.683594 C 80.351562 201.683594 75.550781 206.484375 75.550781 212.355469 C 75.550781 218.222656 80.351562 223.027344 86.222656 223.027344 Z M 86.222656 223.027344 "
                                    style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                    fill="currentColor"></path>
                                <path
                                    d="M 373.59375 363.136719 L 329.738281 410.410156 L 329.738281 293.882812 C 329.738281 288.011719 324.933594 283.210938 319.066406 283.210938 C 313.195312 283.210938 308.394531 288.011719 308.394531 293.882812 L 308.394531 410.410156 L 264.214844 363.136719 C 260.160156 358.871094 253.332031 358.550781 249.0625 362.605469 C 244.792969 366.660156 244.472656 373.382812 248.53125 377.652344 L 310.957031 444.773438 C 312.984375 446.90625 315.757812 448.1875 318.746094 448.1875 C 321.734375 448.1875 324.507812 446.90625 326.535156 444.773438 L 389.070312 377.652344 C 393.125 373.382812 392.910156 366.554688 388.640625 362.605469 C 384.265625 358.550781 377.652344 358.871094 373.59375 363.136719 Z M 373.59375 363.136719 "
                                    style=" stroke:none;fill-rule:nonzero;fill:rgb(0%,0%,0%);fill-opacity:1;"
                                    fill="currentColor"></path>
                            </g>
                        </svg>
                    </a>
                <?php endif; ?>
            </div>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\wamp64\www\mymd-care\Modules/Appointment\resources/views/appointment/api/list.blade.php ENDPATH**/ ?>